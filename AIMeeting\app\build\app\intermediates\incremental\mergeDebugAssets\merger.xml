<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":path_provider_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\path_provider_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":permission_handler_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\permission_handler_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":flutter_inappwebview_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\flutter_inappwebview_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\main\assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\android\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="G:\Projects\Codeup\Work\BlueWhale\JNZ\Meeting\AIMeeting\app\build\app\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>