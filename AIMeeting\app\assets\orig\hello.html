<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hello World</title>
    <style>
        /* 引入谷歌字体 */
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@600&display=swap');

        /* 基本样式重置 */
        body, html {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            overflow: hidden; /* 防止滚动 */
            font-family: 'Poppins', sans-serif; /* 应用自定义字体 */
        }

        /* 容器样式 */
        .container {
            display: flex;
            justify-content: center; /* 水平居中 */
            align-items: center; /* 垂直居中 */
            width: 100%;
            height: 100%;
            /* 渐变背景 */
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        /* "Hello World" 文本样式 */
        .hello-world {
            font-size: 3rem; /* 使用rem单位以适应不同屏幕 */
            color: white;
            text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.3); /* 文字阴影，增加立体感 */
            animation: fadeIn 2s ease-in-out; /* 应用淡入动画 */
        }

        /* 媒体查询，针对小屏幕设备调整字体大小 */
        @media (max-width: 600px) {
            .hello-world {
                font-size: 2.5rem;
            }
        }

        /* 定义淡入动画 */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px); /* 从下方20px处开始动画 */
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>

<div class="container">
    <h1 class="hello-world">Hello World</h1>
</div>

</body>
</html>