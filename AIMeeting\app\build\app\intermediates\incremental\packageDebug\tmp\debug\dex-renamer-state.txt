#Sat Aug 02 09:35:35 CST 2025
base.0=G\:\\Projects\\Codeup\\Work\\BlueWhale\\JNZ\\Meeting\\AIMeeting\\app\\build\\app\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes.dex
base.1=G\:\\Projects\\Codeup\\Work\\BlueWhale\\JNZ\\Meeting\\AIMeeting\\app\\build\\app\\intermediates\\dex\\debug\\mergeLibDexDebug\\0\\classes.dex
base.2=G\:\\Projects\\Codeup\\Work\\BlueWhale\\JNZ\\Meeting\\AIMeeting\\app\\build\\app\\intermediates\\dex\\debug\\mergeProjectDexDebug\\0\\classes.dex
base.3=G\:\\Projects\\Codeup\\Work\\BlueWhale\\JNZ\\Meeting\\AIMeeting\\app\\build\\app\\intermediates\\dex\\debug\\mergeProjectDexDebug\\12\\classes.dex
base.4=G\:\\Projects\\Codeup\\Work\\BlueWhale\\JNZ\\Meeting\\AIMeeting\\app\\build\\app\\intermediates\\dex\\debug\\mergeProjectDexDebug\\1\\classes.dex
path.0=classes.dex
path.1=0/classes.dex
path.2=0/classes.dex
path.3=12/classes.dex
path.4=1/classes.dex
renamed.0=classes.dex
renamed.1=classes2.dex
renamed.2=classes3.dex
renamed.3=classes4.dex
renamed.4=classes5.dex
