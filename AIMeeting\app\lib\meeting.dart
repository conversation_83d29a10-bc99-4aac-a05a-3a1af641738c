import 'package:flutter/material.dart';
import 'dart:async';
import 'package:flutter/services.dart';
import 'dart:io';
import 'package:shelf/shelf_io.dart' as shelf_io;
import 'dart:convert';
import 'package:path_provider/path_provider.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shelf_static/shelf_static.dart';
import 'package:path/path.dart' as p;
import 'package:flutter_localizations/flutter_localizations.dart';

class MeetingControl extends StatefulWidget {
  const MeetingControl({super.key});

  @override
  State<MeetingControl> createState() => _WebviewAppState();
}

class _WebviewAppState extends State<MeetingControl> {
  late final InAppWebViewController _webViewController;
  late final HttpServer _server;

  // JSBridge通信相关
  bool _isCheckingRoute = false;

  // 显示退出确认对话框
  Future<void> _showExitConfirmDialog(String message) async {
    try {
      // 首先尝试使用Flutter原生对话框
      final shouldExit = await showDialog<bool>(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext dialogContext) {
          return Material(
            type: MaterialType.transparency,
            child: AlertDialog(
              title: const Text('退出应用'),
              content: Text(message),
              actions: <Widget>[
                TextButton(
                  child: const Text('取消'),
                  onPressed: () {
                    Navigator.of(dialogContext).pop(false);
                  },
                ),
                TextButton(
                  child: const Text('退出'),
                  onPressed: () {
                    Navigator.of(dialogContext).pop(true);
                  },
                ),
              ],
            ),
          );
        },
      );

      if (shouldExit == true) {
        SystemNavigator.pop();
      }
    } catch (error) {
      print('Flutter对话框失败，尝试H5对话框: $error');

      try {
        // 备用方案：使用H5端的确认对话框
        final result = await _webViewController.evaluateJavascript(
          source: '''
            (function() {
              try {
                return confirm("$message");
              } catch (error) {
                return true; // 如果确认对话框失败，默认退出
              }
            })()
          '''
        );

        print('H5确认对话框结果: $result');

        if (result == true) {
          SystemNavigator.pop();
        }
      } catch (error2) {
        print('所有确认对话框都失败，直接退出: $error2');
        SystemNavigator.pop();
      }
    }
  }

  // String? _serverUrl;

  Future<void> removeNativeSplash() async {
    // 与 MainActivity 中定义的通道名称保持一致
    const platform = MethodChannel('meeting.splash');
    try {
      await platform.invokeMethod('removeSplash');
    } on PlatformException catch (e) {
      print(e);
    }
  }

  Future<void> _createNomediaFileIfNotExists() async {
    Directory dir = await getApplicationDocumentsDirectory();
    String nomediaPath = '${dir.path}/.nomedia';
    File nomediaFile = File(nomediaPath);
    if (!await nomediaFile.exists()) await nomediaFile.create();
  }

  Future<void> _copyAssetsToDirectory(Directory targetDir) async {
    // 创建目标目录
    await targetDir.create(recursive: true);

    // 加载 AssetManifest.json，它包含了所有 asset 的路径列表
    final manifestContent = await rootBundle.loadString('AssetManifest.json');
    final Map<String, dynamic> manifestMap = json.decode(manifestContent);

    // 遍历所有 asset
    final assetKeys =
        manifestMap.keys.where((String key) => key.startsWith('assets/'));

    for (final assetKey in assetKeys) {
      final assetData = await rootBundle.load(assetKey);

      // 创建目标文件路径，并确保子目录存在
      final targetFile =
          File(p.join(targetDir.path, assetKey.replaceFirst('assets/', '')));
      await targetFile.parent.create(recursive: true);

      // 将 asset 数据写入文件
      await targetFile.writeAsBytes(assetData.buffer.asUint8List());
    }
  }

  Future initWebView() async {
    await _createNomediaFileIfNotExists();

    // _webViewController.loadFile(assetFilePath: (await getApplicationDocumentsDirectory()).path + "/Test.html");

    final tempDir = await getTemporaryDirectory();
    final assetsDir = Directory(p.join(tempDir.path, 'assets'));

    if (!await assetsDir.exists()) {
      await _copyAssetsToDirectory(assetsDir);
    }

    _server = await shelf_io.serve(
        createStaticHandler(assetsDir.path,
            listDirectories: true, defaultDocument: 'index.html'),
        '127.0.0.1',
        44325);
    //     .then((server) {
    //   return server;
    // });
    // setState(() {
    //   _serverUrl = 'http://${_server.address.host}:${_server.port}';
    // });
    await Future.delayed(const Duration(seconds: 1));
    await _webViewController.loadUrl(
      urlRequest: URLRequest(
          url: WebUri('http://${_server.address.host}:${_server.port}')),
    );
    await Future.delayed(const Duration(seconds: 2));
    await removeNativeSplash();
  }

  @override
  void initState() {
    //SystemChrome.setPreferredOrientations([DeviceOrientation.landscapeLeft, DeviceOrientation.landscapeRight]);
    super.initState();
    initWebView();
  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual, overlays: [SystemUiOverlay.bottom]);
    return MaterialApp(
        debugShowCheckedModeBanner: false,
        localizationsDelegates: const [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: const [
          Locale('zh', 'CN'),
          Locale('en', 'US'),
        ],
        home: Builder(builder: (BuildContext newContext) {
          return PopScope(
              canPop: false,
              onPopInvoked: (bool didPop) async {
                // dispose();   // dispose必须由flutter框架自动调用
                if (didPop) return;

                // 防止重复检查
                if (_isCheckingRoute) return;
                _isCheckingRoute = true;

                try {
                  // 使用H5提供的后退按键处理方法
                  final result = await _webViewController.evaluateJavascript(
                    source: 'window.handleBackButton ? window.handleBackButton() : { action: "exit", error: "Handler not ready" }'
                  );

                  print('后退按键处理结果: $result');

                  if (result != null && result is Map) {
                    final action = result['action'] as String?;
                    final message = result['message'] as String?;

                    if (action == 'exit') {
                      // 直接退出APP
                      SystemNavigator.pop();
                    } else if (action == 'confirm_exit') {
                      // 需要确认退出
                      await _showExitConfirmDialog(message ?? '确定要退出应用吗？');
                    } else if (action == 'handled') {
                      // H5已处理操作（如调用结束会议方法），不需要额外操作
                      print('H5已处理操作: ${message ?? 'Unknown'}');
                    } else if (action == 'navigate') {
                      // H5已处理跳转，不需要额外操作
                      print('已跳转到首页');
                    } else {
                      // 未知操作，默认退出
                      SystemNavigator.pop();
                    }
                  } else {
                    // 结果无效，默认退出APP
                    SystemNavigator.pop();
                  }
                } catch (error) {
                  print('后退按键处理错误: $error');
                  // 出错时默认退出APP
                  SystemNavigator.pop();
                } finally {
                  _isCheckingRoute = false;
                }
              },
              child: Scaffold(
                  body: Column(
                children: [
                  Expanded(
                      child: InAppWebView(
                          //initialFile: 'assets/hello.html',
                          //initialUrlRequest: URLRequest(url: WebUri("https://172.16.22.128:8543")),
                          //initialUrlRequest: URLRequest(url: WebUri("http://127.0.0.1:44325/")),
                          initialSettings: InAppWebViewSettings(
                            mediaPlaybackRequiresUserGesture: false,
                            allowsInlineMediaPlayback: true,
                            // iframeAllow: "camera; microphone",
                            // iframeAllowFullscreen: true
                          ),
                          onWebViewCreated: (controller) async {
                            _webViewController = controller;
                            //_webViewController.loadFile(assetFilePath: "assets/www/Test.html");
                            //await _loadLocalHtmlWithCustomSsl();

                            // 添加JSBridge处理器 - 用于后退按键拦截
                            _webViewController.addJavaScriptHandler(
                              handlerName: 'routeStatusHandler',
                              callback: (args) async {
                                if (args.isNotEmpty) {
                                  final data = args[0] as Map<String, dynamic>;
                                  final action = data['action'] as String?;

                                  if (action == 'getCurrentRoute') {
                                    // 返回当前路由状态给H5
                                    return {'success': true};
                                  } else if (action == 'navigateToHome') {
                                    // H5请求跳转到首页
                                    return {'success': true};
                                  }
                                }
                                return {'success': false};
                              },
                            );
                          },
                          // androidOnPermissionRequest: (controller, origin, resources) async {
                          //   return PermissionRequestResponse(resources: resources, action: PermissionRequestResponseAction.GRANT);
                          // },
                          // onPermissionRequest: (controller, request) async {
                          //   return PermissionResponse(resources: request.resources, action: PermissionResponseAction.GRANT);
                          // },
                          onPermissionRequest: (controller, request) async {
                            final resources = <PermissionResourceType>[];
                            if (request.resources
                                .contains(PermissionResourceType.CAMERA)) {
                              final cameraStatus =
                                  await Permission.camera.request();
                              if (!cameraStatus.isDenied) {
                                resources.add(PermissionResourceType.CAMERA);
                              }
                            }
                            if (request.resources
                                .contains(PermissionResourceType.MICROPHONE)) {
                              final microphoneStatus =
                                  await Permission.microphone.request();
                              if (!microphoneStatus.isDenied) {
                                resources
                                    .add(PermissionResourceType.MICROPHONE);
                              }
                            }
                            // only for iOS and macOS
                            if (request.resources.contains(
                                PermissionResourceType.CAMERA_AND_MICROPHONE)) {
                              final cameraStatus =
                                  await Permission.camera.request();
                              final microphoneStatus =
                                  await Permission.microphone.request();
                              if (!cameraStatus.isDenied &&
                                  !microphoneStatus.isDenied) {
                                resources.add(PermissionResourceType
                                    .CAMERA_AND_MICROPHONE);
                              }
                            }
                            return PermissionResponse(
                                resources: resources,
                                action: resources.isEmpty
                                    ? PermissionResponseAction.DENY
                                    : PermissionResponseAction.GRANT);
                          },
                          // onReceivedServerTrustAuthRequest: (controller, challenge) async {
                          //   return ServerTrustAuthResponse(action: ServerTrustAuthResponseAction.PROCEED);
                          // },
                          onReceivedHttpAuthRequest:
                              (controller, challenge) async {
                            return HttpAuthResponse(
                                action: HttpAuthResponseAction.PROCEED);
                          }))
                ],
              )));
        }));
  }

  @override
  void dispose() {
    _server.close();
    super.dispose();
  }
}
