/* AI会议记录APP - 自定义样式文件 */

/* 自定义滚动条 */
.custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #475569 #1e293b;
}

.custom-scrollbar::-webkit-scrollbar {
    width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(30, 41, 59, 0.5);
    border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: #475569;
    border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #64748b;
}

/* 渐变文字效果 */
.gradient-text {
    background: linear-gradient(135deg, #3b82f6, #8b5cf6, #06b6d4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 200% 200%;
    animation: gradient-shift 4s ease-in-out infinite;
}

@keyframes gradient-shift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* 玻璃态效果 */
.glass-effect {
    background: rgba(30, 41, 59, 0.3);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 发光效果 */
.glow-effect {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.3);
}

/* 会议进行中的动画效果 */
.recording-pulse {
    animation: recording-pulse 2s ease-in-out infinite;
}

@keyframes recording-pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.8;
    }
}

/* 波纹效果 */
.ripple-effect {
    position: relative;
    overflow: hidden;
}

.ripple-effect::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.ripple-effect:active::before {
    width: 300px;
    height: 300px;
}

/* 加载动画 */
.loading-dots {
    display: inline-block;
}

.loading-dots::after {
    content: '';
    animation: loading-dots 1.5s infinite;
}

@keyframes loading-dots {
    0%, 20% { content: ''; }
    40% { content: '.'; }
    60% { content: '..'; }
    80%, 100% { content: '...'; }
}

/* 音频波形动画 */
.audio-wave {
    display: flex;
    align-items: center;
    gap: 2px;
}

.audio-wave span {
    display: block;
    width: 3px;
    height: 20px;
    background: linear-gradient(to top, #3b82f6, #8b5cf6);
    border-radius: 2px;
    animation: audio-wave 1.2s ease-in-out infinite;
}

.audio-wave span:nth-child(2) { animation-delay: 0.1s; }
.audio-wave span:nth-child(3) { animation-delay: 0.2s; }
.audio-wave span:nth-child(4) { animation-delay: 0.3s; }
.audio-wave span:nth-child(5) { animation-delay: 0.4s; }

@keyframes audio-wave {
    0%, 40%, 100% { transform: scaleY(0.4); }
    20% { transform: scaleY(1); }
}

/* 时间计时器样式 */
.timer-display {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    letter-spacing: 1px;
}

/* 转录文本区域 */
.transcript-area {
    background: rgba(15, 23, 42, 0.5);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 12px;
    padding: 16px;
}

.transcript-text {
    line-height: 1.6;
    word-wrap: break-word;
    font-size: 14px;
}

/* 移动端转录区域优化 */
@media (max-width: 768px) {
    .transcript-area {
        padding: 12px;
        border-radius: 8px;
    }

    .transcript-text {
        font-size: 13px;
        line-height: 1.5;
    }

    /* 移动端转录内容区域高度控制 */
    #meeting-transcript-content {
        /* min-height: 25vh !important;
        max-height: 30vh !important; */
    }

    /* 移动端会议进行中界面优化 */
    #meeting-content {
        height: 100vh;
        overflow-y: auto;
        padding-bottom: 20px; /* 为底部留出额外空间 */
    }

    /* 确保底部控制区域完全可见 */
    #meeting-content .bg-slate-800\/50:last-child {
        margin-bottom: 20px;
    }
}

/* 桌面端转录区域 */
@media (min-width: 769px) {
    #meeting-transcript-content {
        min-height: 60vh;
        max-height: 65vh;
    }

    #meeting-content {
        height: 100vh;
        overflow-y: auto;
    }
}

/* 会议进行中界面的整体布局优化 */
#meeting-content {
    display: flex;
    flex-direction: column;
    max-height: 100vh;
}

#meeting-content.hidden {
    display: none !important;
}

/* 确保转录区域可以正确伸缩 */
.transcript-area {
    flex: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;
}

/* 底部控制区域固定样式 */
#meeting-content > div:last-child {
    flex-shrink: 0; /* 防止底部区域被压缩 */
}

/* 确保详情页正确隐藏 */
#detail-content.hidden {
    display: none !important;
}

/* 确保首页内容正确显示 */
#home-content {
    display: block;
}

#home-content.hidden {
    display: none !important;
}

/* 转录内容样式优化 */
.transcript-sentence {
    padding: 8px 12px;
    margin-bottom: 8px;
    background: rgba(30, 41, 59, 0.3);
    border-radius: 8px;
    border-left: 3px solid rgba(59, 130, 246, 0.5);
    transition: all 0.3s ease;
}

.transcript-sentence:hover {
    background: rgba(30, 41, 59, 0.5);
    border-left-color: rgba(59, 130, 246, 0.8);
}

.transcript-sentence.new {
    animation: slideInFromBottom 0.3s ease-out;
    border-left-color: rgba(34, 197, 94, 0.8);
}

@keyframes slideInFromBottom {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 说话人标识 */
.speaker-label {
    display: inline-block;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-right: 8px;
    margin-bottom: 4px;
}

/* 自定义动画类 */
.animate-slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.animate-slide-down {
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* 按钮悬停效果增强 */
.btn-primary {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-primary:hover::before {
    left: 100%;
}

/* 卡片悬停效果 */
.card-hover {
    transition: all 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* 状态徽章样式 */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
}

.badge-success {
    background-color: rgba(16, 185, 129, 0.2);
    color: #10b981;
}

.badge-warning {
    background-color: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
}

.badge-info {
    background-color: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
}

.badge-error {
    background-color: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.badge-processing {
    background-color: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
}

/* 转录内容样式 */
.transcript-item {
    background: rgba(30, 41, 59, 0.3);
    border-radius: 8px;
    padding: 12px;
    border: 1px solid rgba(71, 85, 105, 0.2);
    transition: all 0.3s ease;
}

.transcript-item:hover {
    border-color: rgba(71, 85, 105, 0.3);
    background: rgba(30, 41, 59, 0.4);
}

.speaker-tag {
    display: inline-flex;
    align-items: center;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
    border: 1px solid rgba(59, 130, 246, 0.3);
}

/* 音频播放器样式 */
.audio-progress-bar {
    position: relative;
    cursor: pointer;
    transition: all 0.2s ease;
}

.audio-progress-bar:hover {
    transform: scaleY(1.2);
}

.audio-progress-handle {
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 12px;
    height: 12px;
    background: #3b82f6;
    border-radius: 50%;
    opacity: 0;
    transition: opacity 0.2s ease;
    cursor: grab;
}

.audio-progress-bar:hover .audio-progress-handle {
    opacity: 1;
}

.audio-progress-handle:active {
    cursor: grabbing;
    transform: translate(-50%, -50%) scale(1.2);
}

/* 背景粒子效果 */
.particle {
    position: absolute;
    border-radius: 50%;
    pointer-events: none;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .glass-effect {
        background: rgba(30, 41, 59, 0.8);
    }
    
    .transcript-area {
        max-height: 200px;
    }
    
    /* 移动端优化 */
    .glow-effect {
        box-shadow: 0 0 20px rgba(59, 130, 246, 0.2);
    }
}

/* 侧边栏动画优化 */
#sidebar {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: linear-gradient(135deg, rgba(71, 85, 105, 0.9), rgba(51, 65, 85, 0.9));
}

/* 侧边栏内容增强 */
#sidebar .filter-btn.active {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

#sidebar .filter-btn:not(.active):hover {
    background: rgba(71, 85, 105, 0.7);
    transform: translateY(-1px);
}

/* 会议列表项增强 */
.meeting-item:hover {
    background: rgba(71, 85, 105, 0.5);
    transform: translateX(4px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* 会议进行中界面样式 */
.recording-pulse {
    animation: recording-pulse 2s ease-in-out infinite;
}

@keyframes recording-pulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 20px rgba(239, 68, 68, 0.4);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 0 30px rgba(239, 68, 68, 0.6);
    }
}

/* 会议进行中布局 */
#meeting-content {
    height: calc(100vh - 120px); /* 减去顶部标题栏高度 */
}

/* 转录区域样式 */
.transcript-area {
    background: rgba(15, 23, 42, 0.6);
    backdrop-filter: blur(10px);
    min-height: 0; /* 允许flex子元素收缩 */
}

.transcript-segment {
    padding: 12px 16px;
    margin-bottom: 8px;
    background: rgba(30, 41, 59, 0.4);
    border-radius: 12px;
    border-left: 3px solid rgba(59, 130, 246, 0.5);
    transition: all 0.3s ease;
}

.transcript-segment:hover {
    background: rgba(30, 41, 59, 0.6);
    border-left-color: rgba(59, 130, 246, 0.8);
    transform: translateX(4px);
}

/* 打字指示器 */
.typing-indicator {
    position: relative;
}

.typing-indicator::after {
    content: '';
    display: inline-block;
    width: 3px;
    height: 16px;
    background: #3b82f6;
    margin-left: 2px;
    animation: typing-cursor 1s infinite;
}

@keyframes typing-cursor {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

/* 音频波形增强 */
.audio-wave span {
    width: 4px;
    height: 24px;
    margin: 0 1px;
    border-radius: 2px;
    animation: audio-wave-enhanced 1.5s ease-in-out infinite;
}

.audio-wave span:nth-child(1) { animation-delay: 0s; }
.audio-wave span:nth-child(2) { animation-delay: 0.1s; }
.audio-wave span:nth-child(3) { animation-delay: 0.2s; }
.audio-wave span:nth-child(4) { animation-delay: 0.3s; }
.audio-wave span:nth-child(5) { animation-delay: 0.4s; }

@keyframes audio-wave-enhanced {
    0%, 40%, 100% {
        transform: scaleY(0.3);
        opacity: 0.6;
    }
    20% {
        transform: scaleY(1);
        opacity: 1;
    }
}

/* 会议控制按钮样式 */
#end-meeting-btn {
    position: relative;
    overflow: hidden;
}

#end-meeting-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

#end-meeting-btn:hover::before {
    left: 100%;
}

/* 会议信息卡片 */
.meeting-info-card {
    transition: all 0.3s ease;
}

.meeting-info-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* 计时器发光效果 */
.timer-display {
    text-shadow:
        0 0 10px rgba(59, 130, 246, 0.5),
        0 0 20px rgba(59, 130, 246, 0.3),
        0 0 30px rgba(59, 130, 246, 0.1);
}

/* 状态指示器 */
.status-indicator {
    animation: status-pulse 2s ease-in-out infinite;
}

@keyframes status-pulse {
    0%, 100% {
        opacity: 0.6;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.1);
    }
}

/* 转录内容滚动优化 */
.transcript-area::-webkit-scrollbar {
    width: 6px;
}

.transcript-area::-webkit-scrollbar-track {
    background: rgba(30, 41, 59, 0.3);
    border-radius: 3px;
}

.transcript-area::-webkit-scrollbar-thumb {
    background: rgba(59, 130, 246, 0.5);
    border-radius: 3px;
}

.transcript-area::-webkit-scrollbar-thumb:hover {
    background: rgba(59, 130, 246, 0.7);
}

/* 会议详情页样式 */
.speaker-avatar {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    flex-shrink: 0;
}

.speaker-label {
    display: inline-block;
    padding: 8px 12px;
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: white;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-align: center;
    min-width: 60px;
}

.conversation-item {
    transition: all 0.3s ease;
    padding: 8px;
    border-radius: 12px;
}

.conversation-item:hover {
    background: rgba(30, 41, 59, 0.3);
    transform: translateX(4px);
}

/* 音频播放器样式 */
#play-pause-btn {
    position: relative;
    overflow: hidden;
}

#play-pause-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

#play-pause-btn:hover::before {
    left: 100%;
}

/* 徽章样式 */
.badge {
    display: inline-flex;
    align-items: center;
    border-radius: 20px;
    font-weight: 600;
    font-size: 12px;
}

.badge-success {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

/* 关键词标签悬停效果 */
.glass-effect .rounded-full {
    transition: all 0.3s ease;
    cursor: pointer;
}

.glass-effect .rounded-full:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* 统计数据动画 */
.glass-effect .flex.justify-between {
    transition: all 0.3s ease;
    padding: 8px 12px;
    border-radius: 8px;
}

.glass-effect .flex.justify-between:hover {
    background: rgba(30, 41, 59, 0.4);
    transform: translateX(4px);
}

/* 参会人员头像动画 */
.glass-effect .w-8.h-8 {
    transition: all 0.3s ease;
}

.glass-effect .w-8.h-8:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* 返回按钮样式 */
#back-to-home {
    transition: all 0.3s ease;
    padding: 8px 16px;
    border-radius: 8px;
}

#back-to-home:hover {
    background: rgba(30, 41, 59, 0.4);
    transform: translateX(-4px);
}

/* 详情页内容区域动画 */
#detail-content .glass-effect {
    transition: all 0.3s ease;
}

#detail-content .glass-effect:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* 进度条样式 */
.progress-bar {
    position: relative;
    overflow: hidden;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* 响应式设计和移动端适配 */

/* 移动端首页优化 */
@media (max-width: 768px) {
    /* 主容器调整 */
    .flex.h-screen {
        flex-direction: column;
    }

    /* 主内容区域 */
    main {
        padding: 1rem;
        overflow-y: auto;
    }

    /* 首页内容 */
    #home-content {
        max-width: 100%;
        padding: 0 1rem;
    }

    /* 话筒图标调整 */
    #home-content .w-20.h-20 {
        width: 4rem;
        height: 4rem;
    }

    /* 标题文字调整 */
    #home-content h1 {
        font-size: 1.75rem;
        line-height: 1.2;
    }

    #home-content p {
        font-size: 0.875rem;
        line-height: 1.4;
    }

    /* 开始会议按钮 */
    #start-meeting-btn {
        padding: 0.875rem 1.5rem;
        font-size: 1rem;
    }

    /* 功能卡片网格 */
    .grid.grid-cols-1.md\\:grid-cols-3 {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    /* 功能卡片内容 */
    .glass-effect .w-10.h-10 {
        width: 2.5rem;
        height: 2.5rem;
    }

    .glass-effect h3 {
        font-size: 1rem;
    }

    .glass-effect p {
        font-size: 0.75rem;
        line-height: 1.3;
    }
}

/* 移动端会议进行中界面优化 */
@media (max-width: 768px) {
    #meeting-content {
        height: 100vh !important;
        padding: 0.5rem !important;
        overflow: hidden !important; /* 防止整体滚动 */
        display: flex;
        flex-direction: column;
    }

    /* 顶部状态栏优化 */
    #meeting-content > div:first-child {
        flex-shrink: 0;
        margin-bottom: 0.25rem !important;
        padding: 0.375rem !important;
        min-height: auto !important;
        max-height: 120px !important; /* 限制顶部最大高度 */
    }

    /* 第一行：录制状态 + 会议标题 + 音频波形 */
    #meeting-content > div:first-child > div:first-child {
        margin-bottom: 0.5rem !important;
    }

    /* 会议标题区域 */
    #meeting-content h2 {
        font-size: 1rem !important;
    }

    #meeting-content p {
        font-size: 0.75rem !important;
    }

    /* 录制状态指示器 */
    .recording-pulse {
        width: 2rem !important;
        height: 2rem !important;
    }

    /* 计时器优化 */
    .timer-display {
        font-size: 1.25rem !important;
    }

    /* 音频波形优化 */
    .audio-wave span {
        width: 2px;
        height: 12px;
    }

    /* 统计信息优化 */
    #meeting-content .flex.items-center.space-x-2 {
        gap: 0.5rem !important;
        flex-shrink: 0 !important;
    }

    #meeting-content .flex.items-center.space-x-4 {
        gap: 0.75rem !important;
    }

    /* 第二行布局优化 */
    #meeting-content .flex.items-center.justify-between {
        flex-wrap: nowrap !important;
        align-items: center !important;
    }

    /* 转录区域优化 */
    #meeting-content .transcript-area {
        flex: 1 !important;
        min-height: 0 !important;
        margin-bottom: 0.25rem !important;
        padding: 0.5rem !important;
        max-height: calc(100vh - 320px) !important; /* 为顶部和底部留出更多空间 */
    }

    #meeting-content #meeting-transcript-content {
        /* min-height: 20vh !important;
        max-height: 25vh !important; */
        font-size: 0.875rem !important;
    }

    /* 底部控制区域优化 */
    #meeting-content > div:last-child {
        flex-shrink: 0 !important;
        padding: 0.375rem !important;
        margin-bottom: 0;
        min-height: auto !important;
        height: auto !important;
        max-height: 80px !important; /* 限制底部最大高度 */
    }

    /* 移动端按钮优化 */
    #meeting-content .flex.items-center.space-x-3 {
        gap: 0.5rem !important;
    }

    #meeting-content .flex.items-center.space-x-3 button {
        flex: 1;
        padding: 0.5rem 0.375rem !important;
        font-size: 0.75rem !important;
        min-height: 40px !important; /* 稍微减少高度但保持触摸友好 */
        max-height: 40px !important;
    }

    /* 确保会议进行中界面在移动端正确显示 */
    #meeting-content.hidden {
        display: none !important;
    }

    #meeting-content:not(.hidden) {
        display: flex !important;
        flex-direction: column !important;
    }

    /* 移动端顶部栏优化 */
    #meeting-content .bg-slate-800\/50 {
        background: rgba(30, 41, 59, 0.8) !important;
        backdrop-filter: blur(10px) !important;
    }

    /* 移动端转录文本优化 */
    #meeting-content .transcript-sentence {
        padding: 0.5rem 0.75rem !important;
        margin-bottom: 0.5rem !important;
        font-size: 0.875rem !important;
        line-height: 1.4 !important;
    }

    /* 强制确保布局正确 */
    #meeting-content {
        max-height: 86vh !important;
        min-height: 86vh !important;
    }

    /* 确保转录区域不会过高 */
    #meeting-content .flex-1.flex.flex-col.min-h-0 {
        flex: 1 !important;
        max-height: 90vh !important;
        overflow: hidden !important;
    }
}

/* 移动端详情页优化 */
@media (max-width: 768px) {
    #detail-content {
        max-width: 100%;
        padding: 1rem;
        height: 100vh;
        overflow-y: auto;
    }

    /* 详情页头部 */
    #detail-content .flex.items-center.justify-between {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    /* 详情页玻璃效果卡片 */
    #detail-content .glass-effect {
        margin-bottom: 1rem;
        padding: 1rem;
    }

    /* 详情页按钮组 */
    #detail-content .flex.space-x-4 {
        flex-direction: column;
        space-x: 0;
        gap: 0.5rem;
        width: 100%;
    }

    #detail-content .flex.space-x-4 > * {
        width: 100%;
    }

    /* 会议标题 */
    #meeting-title {
        font-size: 1.5rem;
        line-height: 1.3;
    }

    /* 会议信息 */
    .flex.flex-wrap.items-center.gap-4 {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    /* 音频播放器 */
    .flex.items-center.space-x-4 {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    #play-pause-btn {
        align-self: center;
    }

    /* 内容区域网格 */
    .grid.grid-cols-1.lg\\:grid-cols-3 {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    /* AI摘要和对话记录 */
    .glass-effect h2 {
        font-size: 1.125rem;
    }

    .conversation-item .bg-slate-800\/50 {
        padding: 0.75rem;
    }

    .speaker-label {
        font-size: 0.625rem;
        padding: 0.375rem 0.75rem;
        min-width: 3rem;
    }

    /* 侧边信息区域 */
    .space-y-6 {
        gap: 1rem;
    }

    .glass-effect h3 {
        font-size: 1rem;
    }

    /* 关键词标签 */
    .flex.flex-wrap.gap-2 span {
        font-size: 0.75rem;
        padding: 0.25rem 0.75rem;
    }

    /* 参会人员 */
    .w-8.h-8 {
        width: 2rem;
        height: 2rem;
        font-size: 0.75rem;
    }
}

/* 移动端侧边栏优化 */
@media (max-width: 768px) {
    #sidebar {
        width: 85%;
        max-width: 320px;
    }

    /* 搜索输入框 */
    #search-input {
        font-size: 1rem;
        padding: 0.75rem 1rem 0.75rem 2.5rem;
    }

    /* 筛选按钮 */
    .filter-btn {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
    }

    /* 会议列表项 */
    .meeting-item {
        padding: 0.75rem;
    }

    /* 空状态 */
    #empty-state, #no-results-state {
        height: 12rem;
        padding: 1.5rem;
    }

    #empty-state .w-16.h-16,
    #no-results-state .w-16.h-16 {
        width: 3rem;
        height: 3rem;
    }

    #empty-state p,
    #no-results-state p {
        font-size: 0.875rem;
    }
}

/* 超小屏幕优化 (320px-480px) */
@media (max-width: 480px) {
    /* 主内容区域 */
    main {
        padding: 0.75rem;
    }

    /* 首页内容 */
    #home-content h1 {
        font-size: 1.5rem;
    }

    #home-content .w-20.h-20 {
        width: 3.5rem;
        height: 3.5rem;
    }

    /* 开始会议按钮 */
    #start-meeting-btn {
        padding: 0.75rem 1.25rem;
        font-size: 0.875rem;
    }

    /* 功能卡片 */
    .glass-effect {
        padding: 0.75rem;
    }

    /* 会议进行中界面 - 超小屏幕优化 */
    #meeting-content .timer-display {
        font-size: 1rem !important;
    }

    #meeting-content .transcript-area {
        max-height: 35vh !important;
        padding: 0.5rem !important;
    }

    #meeting-content h2 {
        font-size: 0.875rem !important;
    }

    #meeting-content p {
        font-size: 0.625rem !important;
    }

    /* 详情页 */
    #detail-content {
        padding: 0.75rem;
        height: 100vh;
        overflow-y: auto;
    }

    #meeting-title {
        font-size: 1.25rem;
    }

    /* 移动端详情页内容区域 */
    #detail-content > div {
        margin-bottom: 0.75rem;
    }

    /* 移动端音频播放器 */
    #detail-content .audio-player {
        padding: 0.75rem;
    }

    /* 移动端转录内容 */
    #detail-content .transcript-content {
        max-height: 40vh;
        overflow-y: auto;
    }

    /* 音频播放器 */
    .bg-slate-800\/50 {
        padding: 0.75rem;
    }

    #play-pause-btn {
        width: 2.5rem;
        height: 2.5rem;
    }

    /* 对话记录 */
    .conversation-item .bg-slate-800\/50 {
        padding: 0.5rem;
    }

    .speaker-avatar {
        width: 2rem;
        height: 2rem;
    }

    .speaker-label {
        font-size: 0.5rem;
        padding: 0.25rem 0.5rem;
        min-width: 2.5rem;
    }
}

/* 横屏模式优化 */
@media (max-width: 768px) and (orientation: landscape) {
    /* 减少垂直间距 */
    #home-content .mb-6 {
        margin-bottom: 1rem;
    }

    #home-content .mb-8 {
        margin-bottom: 1.5rem;
    }

    /* 功能卡片网格调整 */
    .grid.grid-cols-1.md\\:grid-cols-3 {
        grid-template-columns: repeat(3, 1fr);
        gap: 0.75rem;
    }

    /* 会议进行中界面调整 - 已移至专门的移动端优化区域 */

    /* 详情页调整 */
    .grid.grid-cols-1.lg\\:grid-cols-3 {
        grid-template-columns: 2fr 1fr;
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    /* 增加触摸目标大小 */
    button {
        min-height: 44px;
        min-width: 44px;
    }

    .meeting-item {
        min-height: 60px;
    }

    .filter-btn {
        min-height: 36px;
        padding: 0.5rem 1rem;
    }

    /* 移除悬停效果 */
    .glass-effect:hover,
    .meeting-item:hover,
    .conversation-item:hover {
        transform: none;
        background: inherit;
    }

    /* 增强点击反馈 */
    button:active {
        transform: scale(0.95);
        transition: transform 0.1s;
    }

    .meeting-item:active {
        background: rgba(30, 41, 59, 0.4);
        transition: background 0.1s;
    }
}

/* 主内容区域优化 */
main {
    min-height: 100vh;
}

/* 确保内容在移动端正确显示 */
@media (max-width: 767px) {
    #home-content {
        padding-bottom: 2rem;
    }
    
    .grid {
        gap: 1rem;
    }
}

/* 动态背景优化 */
.animate-float {
    animation-duration: 6s;
    animation-timing-function: ease-in-out;
    animation-iteration-count: infinite;
}

/* 新增动画效果 */
.animate-spin-slow {
    animation: spin 20s linear infinite;
}

.animate-spin-reverse {
    animation: spin-reverse 15s linear infinite;
}

.animate-draw-line {
    stroke-dasharray: 1000;
    stroke-dashoffset: 1000;
    animation: draw-line 8s ease-in-out infinite;
}

@keyframes spin-reverse {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(-360deg);
    }
}

@keyframes draw-line {
    0% {
        stroke-dashoffset: 1000;
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    50% {
        stroke-dashoffset: 0;
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        stroke-dashoffset: -1000;
        opacity: 0;
    }
}

/* 增强的背景效果 */
.bg-animated-gradient {
    background: linear-gradient(-45deg, #0f172a, #1e293b, #334155, #1e293b);
    background-size: 400% 400%;
    animation: gradient-shift 15s ease infinite;
}

@keyframes gradient-shift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* 光晕脉冲效果 */
.glow-pulse {
    animation: glow-pulse 3s ease-in-out infinite;
}

@keyframes glow-pulse {
    0%, 100% {
        box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
        transform: scale(1);
    }
    50% {
        box-shadow: 0 0 40px rgba(59, 130, 246, 0.6);
        transform: scale(1.02);
    }
}

/* 粒子闪烁效果 */
.particle-twinkle {
    animation: particle-twinkle 2s ease-in-out infinite;
}

@keyframes particle-twinkle {
    0%, 100% {
        opacity: 0.3;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.2);
    }
}

/* 几何形状浮动 */
.geometric-float {
    animation: geometric-float 8s ease-in-out infinite;
}

@keyframes geometric-float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    25% {
        transform: translateY(-10px) rotate(90deg);
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
    }
    75% {
        transform: translateY(-10px) rotate(270deg);
    }
}

/* 光束扫描效果 */
.light-beam {
    animation: light-beam 4s linear infinite;
}

@keyframes light-beam {
    0% {
        opacity: 0;
        transform: translateX(-100%);
    }
    50% {
        opacity: 1;
    }
    100% {
        opacity: 0;
        transform: translateX(100%);
    }
}

/* 增强的渐变文字效果 */
.gradient-text {
    background: linear-gradient(135deg, #3b82f6, #8b5cf6, #06b6d4, #3b82f6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 300% 300%;
    animation: gradient-text-flow 6s ease-in-out infinite;
}

@keyframes gradient-text-flow {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

/* 确保按钮在小屏幕上可见 */
@media (max-height: 600px) {
    #home-content {
        transform: scale(0.9);
    }
}

/* 背景层级优化 */
.bg-layer-1 {
    z-index: 1;
}

.bg-layer-2 {
    z-index: 2;
}

.bg-layer-3 {
    z-index: 3;
}

/* 增强的按钮效果 */
.btn-primary {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    background-size: 200% 200%;
    animation: gradient-button 4s ease infinite;
}

@keyframes gradient-button {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s;
}

.btn-primary:hover::before {
    left: 100%;
}

/* 卡片增强效果 */
.card-hover {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.card-hover:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.4),
        0 0 20px rgba(59, 130, 246, 0.2);
}

/* 图标脉冲效果 */
.icon-pulse {
    animation: icon-pulse 2s ease-in-out infinite;
}

@keyframes icon-pulse {
    0%, 100% {
        transform: scale(1);
        filter: brightness(1);
    }
    50% {
        transform: scale(1.1);
        filter: brightness(1.2);
    }
}

/* 文字发光效果 */
.text-glow {
    text-shadow:
        0 0 10px rgba(59, 130, 246, 0.5),
        0 0 20px rgba(59, 130, 246, 0.3),
        0 0 30px rgba(59, 130, 246, 0.1);
}

/* 边框发光动画 */
.border-glow {
    position: relative;
}

.border-glow::before {
    content: '';
    position: absolute;
    inset: -2px;
    padding: 2px;
    background: linear-gradient(45deg, #3b82f6, #8b5cf6, #06b6d4, #3b82f6);
    border-radius: inherit;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    animation: border-glow-rotate 3s linear infinite;
}

@keyframes border-glow-rotate {
    0% {
        background-position: 0% 50%;
    }
    100% {
        background-position: 200% 50%;
    }
}

/* 悬浮粒子效果 */
.floating-particles {
    position: relative;
}

.floating-particles::before,
.floating-particles::after {
    content: '';
    position: absolute;
    width: 4px;
    height: 4px;
    background: radial-gradient(circle, #3b82f6, transparent);
    border-radius: 50%;
    animation: float-particle 6s ease-in-out infinite;
}

.floating-particles::before {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.floating-particles::after {
    bottom: 20%;
    right: 10%;
    animation-delay: 3s;
}

@keyframes float-particle {
    0%, 100% {
        transform: translateY(0px) scale(1);
        opacity: 0.7;
    }
    50% {
        transform: translateY(-20px) scale(1.2);
        opacity: 1;
    }
}

/* 呼吸光效 */
.breathing-glow {
    animation: breathing-glow 4s ease-in-out infinite;
}

@keyframes breathing-glow {
    0%, 100% {
        box-shadow:
            0 0 20px rgba(59, 130, 246, 0.3),
            inset 0 0 20px rgba(59, 130, 246, 0.1);
    }
    50% {
        box-shadow:
            0 0 40px rgba(59, 130, 246, 0.6),
            inset 0 0 30px rgba(59, 130, 246, 0.2);
    }
}
