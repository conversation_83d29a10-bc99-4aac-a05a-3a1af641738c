/* AI会议记录APP - 自定义样式文件 */

/* 移动端视口优化 */
:root {
    --vh: 1vh; /* 动态视口高度变量 */

    /* 深空科技配色变量 */
    --deep-space-primary: #0f172a;
    --deep-space-secondary: #1e293b;
    --deep-space-accent: #334155;
    --deep-space-dark: #020617;

    /* 科技光晕色 */
    --tech-blue: rgba(59, 130, 246, 0.15);
    --tech-purple: rgba(139, 92, 246, 0.12);
    --tech-cyan: rgba(6, 182, 212, 0.1);
    --tech-indigo: rgba(99, 102, 241, 0.08);

    /* 图案和效果透明度 */
    --pattern-opacity: 0.03;
    --glow-opacity: 0.1;
    --grid-opacity: 0.05;
}

/* ========================================
   高性能深空科技背景系统
   ======================================== */

/* 主背景容器 - 使用CSS多重背景技术 */
.bg-deep-space {
    /* 多层背景叠加 - 从上到下渲染 */
    background:
        /* 层1：微妙的点阵图案 - 创造科技感纹理 */
        radial-gradient(circle at 25% 25%, var(--tech-blue) 1px, transparent 1px),
        radial-gradient(circle at 75% 75%, var(--tech-purple) 1px, transparent 1px),

        /* 层2：大型光晕效果 - 营造空间深度 */
        radial-gradient(ellipse 800px 600px at 20% 30%, var(--tech-blue) 0%, transparent 50%),
        radial-gradient(ellipse 600px 800px at 80% 70%, var(--tech-purple) 0%, transparent 50%),
        radial-gradient(ellipse 400px 300px at 60% 20%, var(--tech-cyan) 0%, transparent 50%),
        radial-gradient(ellipse 300px 400px at 40% 80%, var(--tech-indigo) 0%, transparent 50%),

        /* 层3：主背景渐变 - 深空基调 */
        radial-gradient(ellipse at center, var(--deep-space-secondary) 0%, var(--deep-space-primary) 70%, var(--deep-space-dark) 100%);

    /* 背景尺寸精确控制 */
    background-size:
        40px 40px,      /* 点阵图案1 */
        60px 60px,      /* 点阵图案2 */
        100% 100%,      /* 光晕1 */
        100% 100%,      /* 光晕2 */
        100% 100%,      /* 光晕3 */
        100% 100%,      /* 光晕4 */
        100% 100%;      /* 主背景 */

    /* 背景位置优化 - 创造错位层次感 */
    background-position:
        0 0,            /* 点阵图案1 */
        20px 20px,      /* 点阵图案2 - 错位排列 */
        0 0,            /* 光晕1 */
        0 0,            /* 光晕2 */
        0 0,            /* 光晕3 */
        0 0,            /* 光晕4 */
        0 0;            /* 主背景 */

    /* 性能优化 - 启用硬件加速 */
    transform: translateZ(0);
    backface-visibility: hidden;
    will-change: auto;
}

/* 使用伪元素添加网格图案层 */
.bg-deep-space::before {
    content: '';
    position: absolute;
    inset: 0;
    background:
        /* 精细网格图案 - 增强科技感 */
        linear-gradient(var(--tech-blue) 1px, transparent 1px),
        linear-gradient(90deg, var(--tech-blue) 1px, transparent 1px);
    background-size: 100px 100px;
    opacity: var(--grid-opacity);
    pointer-events: none;
    /* 硬件加速 */
    transform: translateZ(0);
}

/* 使用伪元素添加边缘渐变遮罩 */
.bg-deep-space::after {
    content: '';
    position: absolute;
    inset: 0;
    background:
        /* 边缘柔化效果 - 增强空间感 */
        radial-gradient(ellipse at center, transparent 30%, rgba(15, 23, 42, 0.2) 70%, rgba(15, 23, 42, 0.4) 100%);
    pointer-events: none;
    /* 硬件加速 */
    transform: translateZ(0);
}

/* 移动端优化 - 简化背景以保证性能 */
@media (max-width: 768px) {
    .bg-deep-space {
        /* 移动端使用简化版背景 */
        background:
            /* 保留主要光晕效果 */
            radial-gradient(ellipse 600px 400px at 30% 40%, var(--tech-blue) 0%, transparent 50%),
            radial-gradient(ellipse 400px 600px at 70% 60%, var(--tech-purple) 0%, transparent 50%),
            /* 简化主背景 */
            linear-gradient(135deg, var(--deep-space-secondary) 0%, var(--deep-space-primary) 50%, var(--deep-space-dark) 100%);
        background-size: 100% 100%, 100% 100%, 100% 100%;
    }

    /* 移动端简化网格 */
    .bg-deep-space::before {
        background-size: 80px 80px;
        opacity: calc(var(--grid-opacity) * 0.7);
    }
}

/* 超小屏幕进一步优化 */
@media (max-width: 480px) {
    .bg-deep-space {
        /* 最简化背景 */
        background:
            radial-gradient(ellipse at 30% 40%, var(--tech-blue) 0%, transparent 60%),
            linear-gradient(135deg, var(--deep-space-secondary) 0%, var(--deep-space-primary) 100%);
    }

    /* 移除网格以节省性能 */
    .bg-deep-space::before {
        display: none;
    }
}

/* 高分辨率屏幕优化 */
@media (min-resolution: 2dppx) {
    .bg-deep-space {
        /* 高DPI屏幕使用更精细的图案 */
        background-size:
            20px 20px,      /* 更精细的点阵 */
            30px 30px,      /* 更精细的点阵 */
            100% 100%, 100% 100%, 100% 100%, 100% 100%, 100% 100%;
    }
}

/* 主容器视口优化 */
.flex.h-screen {
    height: 100vh;
    height: calc(var(--vh, 1vh) * 100);
    min-height: 100vh;
    min-height: calc(var(--vh, 1vh) * 100);
}

/* 首页和详情页面的差异化滚动处理 */
/* 默认body禁止滚动，防止首页有滚动条 */
body.home-page {
    overflow: hidden;
    height: 100vh;
    height: calc(var(--vh, 1vh) * 100);
    max-height: 100vh;
    max-height: calc(var(--vh, 1vh) * 100);
    overscroll-behavior: none; /* 禁用弹性滚动 */
    touch-action: manipulation; /* 保持触摸交互，但优化滚动 */
}

/* 详情页面时允许滚动 */
body.detail-page {
    overflow: hidden; /* body仍然禁止滚动，但main容器允许 */
    height: 100vh;
    height: calc(var(--vh, 1vh) * 100);
    max-height: 100vh;
    max-height: calc(var(--vh, 1vh) * 100);
}

/* 会议页面时禁止滚动（类似首页） */
body.meeting-page {
    overflow: hidden;
    height: 100vh;
    height: calc(var(--vh, 1vh) * 100);
    max-height: 100vh;
    max-height: calc(var(--vh, 1vh) * 100);
}

/* 详情页面主容器滚动设置 */
body.detail-page main {
    overflow-y: auto;
    height: calc(100vh - 80px); /* 减去顶部导航栏高度 */
    -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */
}

/* 详情页面内容容器 */
body.detail-page #detail-content {
    overflow: visible !important;
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
    padding-bottom: 2rem; /* 底部留白确保内容完整显示 */
}

/* 移动端详情页面特殊处理 */
@media (max-width: 768px) {
    body.detail-page main {
        height: calc(100vh - 70px); /* 移动端导航栏稍矮 */
    }
    
    body.detail-page #detail-content {
        padding-bottom: 3rem; /* 移动端留更多底部空间 */
    }
}

/* 超小屏幕设备优化 */
@media (max-width: 480px) {
    body.detail-page main {
        height: calc(100vh - 60px); /* 超小屏幕导航栏更矮 */
    }
    
    body.detail-page #detail-content {
        padding-bottom: 4rem; /* 更多底部空间确保内容不被遮挡 */
    }
}

/* 自定义滚动条 */
.custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #475569 #1e293b;
}

.custom-scrollbar::-webkit-scrollbar {
    width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(30, 41, 59, 0.5);
    border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: #475569;
    border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #64748b;
}

/* 渐变文字效果 */
.gradient-text {
    background: linear-gradient(135deg, #3b82f6, #8b5cf6, #06b6d4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 200% 200%;
    animation: gradient-shift 4s ease-in-out infinite;
}

@keyframes gradient-shift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* 玻璃态效果 - 性能优化版本 */
.glass-effect {
    background: rgba(30, 41, 59, 0.85);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 发光效果 */
.glow-effect {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.3);
}

/* 会议进行中的动画效果 */
.recording-pulse {
    animation: recording-pulse 2s ease-in-out infinite;
}

@keyframes recording-pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.8;
    }
}

/* 波纹效果 */
.ripple-effect {
    position: relative;
    overflow: hidden;
}

.ripple-effect::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.ripple-effect:active::before {
    width: 300px;
    height: 300px;
}

/* 禁用开始会议按钮的所有动画效果 */
#start-meeting-btn {
    animation: none !important;
    transition: none !important;
    transform: none !important;
}

#start-meeting-btn:hover {
    transform: none !important;
    animation: none !important;
}

#start-meeting-btn.ripple-effect::before {
    display: none !important;
}

#start-meeting-btn.glow-pulse {
    animation: none !important;
}

/* 加载动画 */
.loading-dots {
    display: inline-block;
}

.loading-dots::after {
    content: '';
    animation: loading-dots 1.5s infinite;
}

@keyframes loading-dots {
    0%, 20% { content: ''; }
    40% { content: '.'; }
    60% { content: '..'; }
    80%, 100% { content: '...'; }
}

/* 音频波形动画 */
.audio-wave {
    display: flex;
    align-items: center;
    gap: 2px;
}

.audio-wave span {
    display: block;
    width: 3px;
    height: 20px;
    background: linear-gradient(to top, #3b82f6, #8b5cf6);
    border-radius: 2px;
    animation: audio-wave 1.2s ease-in-out infinite;
}

.audio-wave span:nth-child(2) { animation-delay: 0.1s; }
.audio-wave span:nth-child(3) { animation-delay: 0.2s; }
.audio-wave span:nth-child(4) { animation-delay: 0.3s; }
.audio-wave span:nth-child(5) { animation-delay: 0.4s; }

@keyframes audio-wave {
    0%, 40%, 100% { transform: scaleY(0.4); }
    20% { transform: scaleY(1); }
}

/* 时间计时器样式 */
.timer-display {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    letter-spacing: 1px;
}

/* 转录文本区域 */
.transcript-area {
    background: rgba(15, 23, 42, 0.5);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 12px;
    padding: 16px;
}

.transcript-text {
    line-height: 1.6;
    word-wrap: break-word;
    font-size: 14px;
}

/* 移动端转录区域优化 */
@media (max-width: 768px) {
    .transcript-area {
        padding: 12px;
        border-radius: 8px;
    }

    .transcript-text {
        font-size: 13px;
        line-height: 1.5;
    }

    /* 移动端转录内容区域高度控制 */
    #meeting-transcript-content {
        /* min-height: 25vh !important;
        max-height: 30vh !important; */
    }

    /* 移动端会议进行中界面优化 */
    #meeting-content {
        height: 100vh;
        overflow-y: auto;
        padding-bottom: 20px; /* 为底部留出额外空间 */
    }

    /* 确保底部控制区域完全可见 */
    #meeting-content .bg-slate-800\/50:last-child {
        margin-bottom: 20px;
    }
}

/* 桌面端转录区域 */
@media (min-width: 769px) {
    #meeting-transcript-content {
        min-height: 60vh;
        max-height: 65vh;
    }

    #meeting-content {
        height: 100vh;
        overflow-y: auto;
    }
}

/* 会议进行中界面的整体布局优化 */
#meeting-content {
    display: flex;
    flex-direction: column;
    max-height: 100vh;
}

#meeting-content.hidden {
    display: none !important;
}

/* 确保转录区域可以正确伸缩 */
.transcript-area {
    flex: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;
}

/* 底部控制区域固定样式 */
#meeting-content > div:last-child {
    flex-shrink: 0; /* 防止底部区域被压缩 */
}

/* 确保详情页正确隐藏 */
#detail-content.hidden {
    display: none !important;
}

/* 确保首页内容正确显示 */
#home-content {
    display: block;
}

#home-content.hidden {
    display: none !important;
}

/* 转录内容样式优化 */
.transcript-sentence {
    padding: 8px 12px;
    margin-bottom: 8px;
    background: rgba(30, 41, 59, 0.3);
    border-radius: 8px;
    border-left: 3px solid rgba(59, 130, 246, 0.5);
    transition: all 0.3s ease;
}

.transcript-sentence:hover {
    background: rgba(30, 41, 59, 0.5);
    border-left-color: rgba(59, 130, 246, 0.8);
}

.transcript-sentence.new {
    animation: slideInFromBottom 0.3s ease-out;
    border-left-color: rgba(34, 197, 94, 0.8);
}

@keyframes slideInFromBottom {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 说话人标识 */
.speaker-label {
    display: inline-block;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-right: 8px;
    margin-bottom: 4px;
}

/* 自定义动画类 */
.animate-slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.animate-slide-down {
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* 按钮悬停效果增强 */
.btn-primary {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-primary:hover::before {
    left: 100%;
}

/* 卡片悬停效果 */
.card-hover {
    transition: all 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* 状态徽章样式 */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
}

.badge-success {
    background-color: rgba(16, 185, 129, 0.2);
    color: #10b981;
}

.badge-warning {
    background-color: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
}

.badge-info {
    background-color: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
}

.badge-error {
    background-color: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.badge-processing {
    background-color: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
}

/* 转录内容样式 */
.transcript-item {
    background: rgba(30, 41, 59, 0.3);
    border-radius: 8px;
    padding: 12px;
    border: 1px solid rgba(71, 85, 105, 0.2);
    transition: all 0.3s ease;
}

.transcript-item:hover {
    border-color: rgba(71, 85, 105, 0.3);
    background: rgba(30, 41, 59, 0.4);
}

.speaker-tag {
    display: inline-flex;
    align-items: center;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
    border: 1px solid rgba(59, 130, 246, 0.3);
}

/* 音频播放器样式 */
.audio-progress-bar {
    position: relative;
    cursor: pointer;
    transition: all 0.2s ease;
}

.audio-progress-bar:hover {
    transform: scaleY(1.2);
}

.audio-progress-handle {
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 12px;
    height: 12px;
    background: #3b82f6;
    border-radius: 50%;
    opacity: 0;
    transition: opacity 0.2s ease;
    cursor: grab;
}

.audio-progress-bar:hover .audio-progress-handle {
    opacity: 1;
}

.audio-progress-handle:active {
    cursor: grabbing;
    transform: translate(-50%, -50%) scale(1.2);
}

/* 背景粒子效果 */
.particle {
    position: absolute;
    border-radius: 50%;
    pointer-events: none;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .glass-effect {
        background: rgba(30, 41, 59, 0.8);
    }
    
    .transcript-area {
        max-height: 200px;
    }
    
    /* 移动端优化 */
    .glow-effect {
        box-shadow: 0 0 20px rgba(59, 130, 246, 0.2);
    }
}

/* 侧边栏动画优化 */
#sidebar {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: linear-gradient(135deg, rgba(71, 85, 105, 0.9), rgba(51, 65, 85, 0.9));
}

/* 侧边栏内容增强 */
#sidebar .filter-btn.active {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

#sidebar .filter-btn:not(.active):hover {
    background: rgba(71, 85, 105, 0.7);
    transform: translateY(-1px);
}

/* 会议列表项增强 */
.meeting-item:hover {
    background: rgba(71, 85, 105, 0.5);
    transform: translateX(4px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* 会议进行中界面样式 */
.recording-pulse {
    animation: recording-pulse 2s ease-in-out infinite;
}

@keyframes recording-pulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 20px rgba(239, 68, 68, 0.4);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 0 30px rgba(239, 68, 68, 0.6);
    }
}

/* 会议进行中布局 */
#meeting-content {
    height: calc(100vh - 120px); /* 减去顶部标题栏高度 */
}

/* 转录区域样式 - 性能优化版本 */
.transcript-area {
    background: rgba(15, 23, 42, 0.9);
    min-height: 0; /* 允许flex子元素收缩 */
}

.transcript-segment {
    padding: 12px 16px;
    margin-bottom: 8px;
    background: rgba(30, 41, 59, 0.4);
    border-radius: 12px;
    border-left: 3px solid rgba(59, 130, 246, 0.5);
    transition: all 0.3s ease;
}

.transcript-segment:hover {
    background: rgba(30, 41, 59, 0.6);
    border-left-color: rgba(59, 130, 246, 0.8);
    transform: translateX(4px);
}

/* 打字指示器 */
.typing-indicator {
    position: relative;
}

.typing-indicator::after {
    content: '';
    display: inline-block;
    width: 3px;
    height: 16px;
    background: #3b82f6;
    margin-left: 2px;
    animation: typing-cursor 1s infinite;
}

@keyframes typing-cursor {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

/* 音频波形增强 */
.audio-wave span {
    width: 4px;
    height: 24px;
    margin: 0 1px;
    border-radius: 2px;
    animation: audio-wave-enhanced 1.5s ease-in-out infinite;
}

.audio-wave span:nth-child(1) { animation-delay: 0s; }
.audio-wave span:nth-child(2) { animation-delay: 0.1s; }
.audio-wave span:nth-child(3) { animation-delay: 0.2s; }
.audio-wave span:nth-child(4) { animation-delay: 0.3s; }
.audio-wave span:nth-child(5) { animation-delay: 0.4s; }

@keyframes audio-wave-enhanced {
    0%, 40%, 100% {
        transform: scaleY(0.3);
        opacity: 0.6;
    }
    20% {
        transform: scaleY(1);
        opacity: 1;
    }
}

/* 会议控制按钮样式 */
#end-meeting-btn {
    position: relative;
    overflow: hidden;
}

#end-meeting-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

#end-meeting-btn:hover::before {
    left: 100%;
}

/* 会议信息卡片 */
.meeting-info-card {
    transition: all 0.3s ease;
}

.meeting-info-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* 计时器发光效果 */
.timer-display {
    text-shadow:
        0 0 10px rgba(59, 130, 246, 0.5),
        0 0 20px rgba(59, 130, 246, 0.3),
        0 0 30px rgba(59, 130, 246, 0.1);
}

/* 状态指示器 */
.status-indicator {
    animation: status-pulse 2s ease-in-out infinite;
}

@keyframes status-pulse {
    0%, 100% {
        opacity: 0.6;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.1);
    }
}

/* 转录内容滚动优化 */
.transcript-area::-webkit-scrollbar {
    width: 6px;
}

.transcript-area::-webkit-scrollbar-track {
    background: rgba(30, 41, 59, 0.3);
    border-radius: 3px;
}

.transcript-area::-webkit-scrollbar-thumb {
    background: rgba(59, 130, 246, 0.5);
    border-radius: 3px;
}

.transcript-area::-webkit-scrollbar-thumb:hover {
    background: rgba(59, 130, 246, 0.7);
}

/* 会议详情页样式 */
.speaker-avatar {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    flex-shrink: 0;
}

.speaker-label {
    display: inline-block;
    padding: 8px 12px;
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: white;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-align: center;
    min-width: 60px;
}

.conversation-item {
    transition: all 0.3s ease;
    padding: 8px;
    border-radius: 12px;
}

.conversation-item:hover {
    background: rgba(30, 41, 59, 0.3);
    transform: translateX(4px);
}

/* 音频播放器样式 */
#play-pause-btn {
    position: relative;
    overflow: hidden;
}

#play-pause-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

#play-pause-btn:hover::before {
    left: 100%;
}

/* 徽章样式 */
.badge {
    display: inline-flex;
    align-items: center;
    border-radius: 20px;
    font-weight: 600;
    font-size: 12px;
}

.badge-success {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

/* 关键词标签悬停效果 */
.glass-effect .rounded-full {
    transition: all 0.3s ease;
    cursor: pointer;
}

.glass-effect .rounded-full:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* 统计数据动画 */
.glass-effect .flex.justify-between {
    transition: all 0.3s ease;
    padding: 8px 12px;
    border-radius: 8px;
}

.glass-effect .flex.justify-between:hover {
    background: rgba(30, 41, 59, 0.4);
    transform: translateX(4px);
}

/* 参会人员头像动画 */
.glass-effect .w-8.h-8 {
    transition: all 0.3s ease;
}

.glass-effect .w-8.h-8:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* 返回按钮样式 */
#back-to-home {
    transition: all 0.3s ease;
    padding: 8px 16px;
    border-radius: 8px;
}

#back-to-home:hover {
    background: rgba(30, 41, 59, 0.4);
    transform: translateX(-4px);
}

/* 详情页内容区域动画 */
#detail-content .glass-effect {
    transition: all 0.3s ease;
}

#detail-content .glass-effect:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* 进度条样式 */
.progress-bar {
    position: relative;
    overflow: hidden;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* 响应式设计和移动端适配 */

/* 移动端首页优化 */
@media (max-width: 768px) {
    /* 主容器调整 */
    .flex.h-screen {
        flex-direction: column;
    }

    /* 主内容区域 */
    main {
        padding: 1rem;
        overflow-y: auto;
    }

    /* 首页内容 */
    #home-content {
        max-width: 100%;
        padding: 0 1rem;
    }

    /* 话筒图标调整 */
    #home-content .w-20.h-20 {
        width: 4rem;
        height: 4rem;
    }

    /* 标题文字调整 */
    #home-content h1 {
        font-size: 1.75rem;
        line-height: 1.2;
    }

    #home-content p {
        font-size: 0.875rem;
        line-height: 1.4;
    }

    /* 开始会议按钮 */
    #start-meeting-btn {
        padding: 0.875rem 1.5rem;
        font-size: 1rem;
    }

    /* 功能卡片网格 */
    .grid.grid-cols-1.md\\:grid-cols-3 {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    /* 功能卡片内容 */
    .glass-effect .w-10.h-10 {
        width: 2.5rem;
        height: 2.5rem;
    }

    .glass-effect h3 {
        font-size: 1rem;
    }

    .glass-effect p {
        font-size: 0.75rem;
        line-height: 1.3;
    }
}

/* 移动端会议进行中界面优化 */
@media (max-width: 768px) {
    #meeting-content {
        height: 100vh !important;
        padding: 0.5rem !important;
        overflow: hidden !important; /* 防止整体滚动 */
        display: flex;
        flex-direction: column;
    }

    /* 顶部状态栏优化 */
    #meeting-content > div:first-child {
        flex-shrink: 0;
        margin-bottom: 0.25rem !important;
        padding: 0.375rem !important;
        min-height: auto !important;
        max-height: 120px !important; /* 限制顶部最大高度 */
    }

    /* 第一行：录制状态 + 会议标题 + 音频波形 */
    #meeting-content > div:first-child > div:first-child {
        margin-bottom: 0.5rem !important;
    }

    /* 会议标题区域 */
    #meeting-content h2 {
        font-size: 1rem !important;
    }

    #meeting-content p {
        font-size: 0.75rem !important;
    }

    /* 录制状态指示器 */
    .recording-pulse {
        width: 2rem !important;
        height: 2rem !important;
    }

    /* 计时器优化 */
    .timer-display {
        font-size: 1.25rem !important;
    }

    /* 音频波形优化 */
    .audio-wave span {
        width: 2px;
        height: 12px;
    }

    /* 统计信息优化 */
    #meeting-content .flex.items-center.space-x-2 {
        gap: 0.5rem !important;
        flex-shrink: 0 !important;
    }

    #meeting-content .flex.items-center.space-x-4 {
        gap: 0.75rem !important;
    }

    /* 第二行布局优化 */
    #meeting-content .flex.items-center.justify-between {
        flex-wrap: nowrap !important;
        align-items: center !important;
    }

    /* 转录区域优化 */
    #meeting-content .transcript-area {
        flex: 1 !important;
        min-height: 0 !important;
        margin-bottom: 0.25rem !important;
        padding: 0.5rem !important;
        max-height: calc(100vh - 320px) !important; /* 为顶部和底部留出更多空间 */
    }

    #meeting-content #meeting-transcript-content {
        /* min-height: 20vh !important;
        max-height: 25vh !important; */
        font-size: 0.875rem !important;
    }

    /* 底部控制区域优化 */
    #meeting-content > div:last-child {
        flex-shrink: 0 !important;
        padding: 0.375rem !important;
        margin-bottom: 0;
        min-height: auto !important;
        height: auto !important;
        max-height: 80px !important; /* 限制底部最大高度 */
    }

    /* 移动端按钮优化 */
    #meeting-content .flex.items-center.space-x-3 {
        gap: 0.5rem !important;
    }

    #meeting-content .flex.items-center.space-x-3 button {
        flex: 1;
        padding: 0.5rem 0.375rem !important;
        font-size: 0.75rem !important;
        min-height: 40px !important; /* 稍微减少高度但保持触摸友好 */
        max-height: 40px !important;
    }

    /* 确保会议进行中界面在移动端正确显示 */
    #meeting-content.hidden {
        display: none !important;
    }

    #meeting-content:not(.hidden) {
        display: flex !important;
        flex-direction: column !important;
    }

    /* 移动端顶部栏优化 */
    #meeting-content .bg-slate-800\/50 {
        background: rgba(30, 41, 59, 0.9) !important;
    }

    /* 移动端转录文本优化 */
    #meeting-content .transcript-sentence {
        padding: 0.5rem 0.75rem !important;
        margin-bottom: 0.5rem !important;
        font-size: 0.875rem !important;
        line-height: 1.4 !important;
    }

    /* 强制确保布局正确 */
    #meeting-content {
        max-height: 86vh !important;
        min-height: 86vh !important;
    }

    /* 确保转录区域不会过高 */
    #meeting-content .flex-1.flex.flex-col.min-h-0 {
        flex: 1 !important;
        max-height: 90vh !important;
        overflow: hidden !important;
    }
}

/* 移动端详情页优化 */
@media (max-width: 768px) {
    #detail-content {
        max-width: 100%;
        height: calc(100vh - 5rem); /* 减去顶部导航栏高度 */
        overflow-y: auto;
        padding: 0.5rem;
        padding-top: 1rem; /* 确保内容不被导航栏遮挡 */
        /* 确保详情页面在移动端有正确的视口高度 */
        min-height: calc(100vh - 5rem);
        box-sizing: border-box;
        margin-top: 0; /* 重置可能的margin */
    }

    /* 详情页主要内容区域 */
    #detail-content > .flex-1 {
        min-height: 0;
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    /* 详情页内容区域网格布局 */
    #detail-content .flex-1.flex.flex-col.lg\\:flex-row {
        flex-direction: column;
        gap: 1rem;
        min-height: auto;
        height: auto;
        overflow: visible;
    }

    /* 主要内容区域 */
    #detail-content .flex-1.flex.flex-col.space-y-4 {
        min-height: auto;
        flex: none;
        overflow: visible;
        margin-bottom: 1rem;
    }

    /* 会议记录侧边栏在移动端全宽显示 */
    #detail-content .w-full.lg\\:w-96 {
        width: 100%;
        min-height: 300px;
        max-height: none; /* 移除最大高度限制 */
        overflow: visible;
        flex-shrink: 0;
        margin-top: 1rem;
    }

    /* 会议信息卡片确保完整显示 */
    #detail-content .glass-effect {
        margin-bottom: 1rem;
        min-height: auto;
        overflow: visible;
        padding: 1rem;
    }
    
    /* 确保第一个卡片有足够的顶部间距 - 高优先级 */
    #detail-content .glass-effect:first-child {
        margin-top: 4rem !important; /* 使用!important确保不被覆盖 */
        flex-shrink: 0 !important;
        position: relative !important;
    }

    /* 会议摘要区域 */
    #meeting-summary {
        min-height: 150px;
        max-height: none;
        overflow: visible;
        padding: 0.5rem 0;
    }

    /* 详情页头部 */
    #detail-content .flex.items-center.justify-between {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    /* 详情页玻璃效果卡片 */
    #detail-content .glass-effect {
        margin-bottom: 1rem;
        padding: 1rem;
    }

    /* 详情页按钮组 */
    #detail-content .flex.space-x-4 {
        flex-direction: column;
        space-x: 0;
        gap: 0.5rem;
        width: 100%;
    }

    #detail-content .flex.space-x-4 > * {
        width: 100%;
    }

    /* 会议标题 */
    #meeting-title {
        font-size: 1.5rem;
        line-height: 1.3;
    }

    /* 会议信息 */
    .flex.flex-wrap.items-center.gap-4 {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    /* 音频播放器 */
    .flex.items-center.space-x-4 {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    #play-pause-btn {
        align-self: center;
    }

    /* 内容区域网格 */
    .grid.grid-cols-1.lg\\:grid-cols-3 {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    /* AI摘要和对话记录 */
    .glass-effect h2 {
        font-size: 1.125rem;
    }

    /* 会议信息标题区域优化 */
    .glass-effect .flex.items-center.justify-between {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
        margin-bottom: 1rem;
    }

    /* 中等移动端屏幕优化 */
    @media (max-width: 640px) {
        .glass-effect .flex.items-center.space-x-4 {
            gap: 0.75rem;
            justify-content: space-between;
        }

        .glass-effect .flex.items-center.space-x-4 > span {
            flex: 1;
            min-width: 70px;
            font-size: 0.6875rem;
            padding: 0.3125rem 0.375rem;
        }
    }

    /* 会议信息统计数据优化 */
    .glass-effect .flex.items-center.space-x-4 {
        flex-direction: row;
        align-items: center;
        gap: 1rem;
        margin-top: 0;
        flex-wrap: wrap;
        justify-content: flex-start;
    }

    .glass-effect .flex.items-center.space-x-4 > span {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        font-size: 0.75rem;
        white-space: nowrap;
        background: rgba(34, 197, 94, 0.1);
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;
        border: 1px solid rgba(34, 197, 94, 0.2);
        min-width: 60px;
        justify-content: center;
    }

    /* 在小屏幕上显示为网格布局 */
    @media (max-width: 480px) {
        .glass-effect .flex.items-center.space-x-4 {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.5rem;
            width: 100%;
        }

        .glass-effect .flex.items-center.space-x-4 > span {
            width: 100%;
            font-size: 0.625rem;
            padding: 0.375rem 0.25rem;
            min-width: auto;
        }
    }

    /* 确保图标和文字对齐 */
    .glass-effect .flex.items-center.space-x-4 i {
        flex-shrink: 0;
        width: 12px;
        text-align: center;
    }

    .conversation-item .bg-slate-800\/50 {
        padding: 0.75rem;
    }

    .speaker-label {
        font-size: 0.625rem;
        padding: 0.375rem 0.75rem;
        min-width: 3rem;
    }

    /* 侧边信息区域 */
    .space-y-6 {
        gap: 1rem;
    }

    .glass-effect h3 {
        font-size: 1rem;
    }

    /* 关键词标签 */
    .flex.flex-wrap.gap-2 span {
        font-size: 0.75rem;
        padding: 0.25rem 0.75rem;
    }

    /* 参会人员 */
    .w-8.h-8 {
        width: 2rem;
        height: 2rem;
        font-size: 0.75rem;
    }
}

/* 超小屏幕优化 (320px-480px) */
@media (max-width: 480px) {
    /* 主内容区域 */
    main {
        padding: 0.75rem;
    }

    /* 首页内容 */
    #home-content h1 {
        font-size: 1.5rem;
    }

    #home-content .w-20.h-20 {
        width: 3.5rem;
        height: 3.5rem;
    }

    /* 开始会议按钮 */
    #start-meeting-btn {
        padding: 0.75rem 1.25rem;
        font-size: 0.875rem;
    }

    /* 功能卡片 */
    .glass-effect {
        padding: 0.75rem;
    }

    /* 会议进行中界面 - 超小屏幕优化 */
    #meeting-content .timer-display {
        font-size: 1rem !important;
    }

    #meeting-content .transcript-area {
        max-height: 35vh !important;
        padding: 0.5rem !important;
    }

    #meeting-content h2 {
        font-size: 0.875rem !important;
    }

    #meeting-content p {
        font-size: 0.625rem !important;
    }

    /* 详情页 */
    #detail-content {
        padding: 0.75rem;
        height: 100vh;
        overflow-y: auto;
    }

    #meeting-title {
        font-size: 1.25rem;
    }

    /* 超小屏幕会议信息统计优化 */
    .glass-effect .flex.items-center.space-x-4 {
        gap: 0.5rem;
    }

    .glass-effect .flex.items-center.space-x-4 > span {
        font-size: 0.625rem;
        padding: 0.125rem 0.375rem;
        min-width: auto;
    }

    .glass-effect .flex.items-center.space-x-4 i {
        width: 10px;
        font-size: 0.625rem;
    }

    /* 移动端详情页内容区域 */
    #detail-content > div {
        margin-bottom: 0.75rem;
    }

    /* 移动端音频播放器 */
    #detail-content .audio-player {
        padding: 0.75rem;
    }

    /* 移动端转录内容 */
    #detail-content .transcript-content {
        max-height: 40vh;
        overflow-y: auto;
    }

    /* 音频播放器 */
    .bg-slate-800\/50 {
        padding: 0.75rem;
    }

    #play-pause-btn {
        width: 2.5rem;
        height: 2.5rem;
    }

    /* 对话记录 */
    .conversation-item .bg-slate-800\/50 {
        padding: 0.5rem;
    }

    .speaker-avatar {
        width: 2rem;
        height: 2rem;
    }

    .speaker-label {
        font-size: 0.5rem;
        padding: 0.25rem 0.5rem;
        min-width: 2.5rem;
    }
}

/* 横屏模式优化 */
@media (max-width: 768px) and (orientation: landscape) {
    /* 减少垂直间距 */
    #home-content .mb-6 {
        margin-bottom: 1rem;
    }

    #home-content .mb-8 {
        margin-bottom: 1.5rem;
    }

    /* 功能卡片网格调整 */
    .grid.grid-cols-1.md\\:grid-cols-3 {
        grid-template-columns: repeat(3, 1fr);
        gap: 0.75rem;
    }

    /* 会议进行中界面调整 - 已移至专门的移动端优化区域 */

    /* 详情页调整 */
    .grid.grid-cols-1.lg\\:grid-cols-3 {
        grid-template-columns: 2fr 1fr;
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    /* 增加触摸目标大小 */
    button {
        min-height: 44px;
        min-width: 44px;
    }

    .meeting-item {
        min-height: 60px;
    }

    .filter-btn {
        min-height: 36px;
        padding: 0.5rem 1rem;
    }

    /* 移除悬停效果 */
    .glass-effect:hover,
    .meeting-item:hover,
    .conversation-item:hover {
        transform: none;
        background: inherit;
    }

    /* 增强点击反馈 */
    button:active {
        transform: scale(0.95);
        transition: transform 0.1s;
    }

    .meeting-item:active {
        background: rgba(30, 41, 59, 0.4);
        transition: background 0.1s;
    }
}

/* 主内容区域优化 */
main {
    min-height: 100vh;
}

/* 确保内容在移动端正确显示 */
@media (max-width: 767px) {
    #home-content {
        padding-bottom: 2rem;
    }
    
    .grid {
        gap: 1rem;
    }
}

/* 动态背景优化 */
.animate-float {
    animation-duration: 6s;
    animation-timing-function: ease-in-out;
    animation-iteration-count: infinite;
}

/* 移除复杂动画以提升性能 */

/* 简化背景效果 - 移除动画 */
.bg-animated-gradient {
    background: linear-gradient(-45deg, #0f172a, #1e293b, #334155, #1e293b);
}

@keyframes gradient-shift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* 简化光晕效果 - 移除动画 */
.glow-pulse {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

/* 简化粒子效果 - 移除动画 */
.particle-twinkle {
    opacity: 0.6;
}

/* 移除几何形状浮动动画 */

/* 移除光束扫描动画 */

/* 简化渐变文字效果 - 移除动画 */
.gradient-text {
    background: linear-gradient(135deg, #3b82f6, #8b5cf6, #06b6d4, #3b82f6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* 确保按钮在小屏幕上可见 */
@media (max-height: 600px) {
    #home-content {
        transform: scale(0.9);
    }
}

/* 背景层级优化 */
.bg-layer-1 {
    z-index: 1;
}

.bg-layer-2 {
    z-index: 2;
}

.bg-layer-3 {
    z-index: 3;
}

/* 增强的按钮效果 */
.btn-primary {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    background-size: 200% 200%;
    animation: gradient-button 4s ease infinite;
}

@keyframes gradient-button {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s;
}

.btn-primary:hover::before {
    left: 100%;
}

/* 卡片增强效果 */
.card-hover {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.card-hover:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.4),
        0 0 20px rgba(59, 130, 246, 0.2);
}

/* 简化图标效果 - 移除动画 */
.icon-pulse {
    filter: brightness(1.1);
}

/* 文字发光效果 */
.text-glow {
    text-shadow:
        0 0 10px rgba(59, 130, 246, 0.5),
        0 0 20px rgba(59, 130, 246, 0.3),
        0 0 30px rgba(59, 130, 246, 0.1);
}

/* 边框发光动画 */
.border-glow {
    position: relative;
}

.border-glow::before {
    content: '';
    position: absolute;
    inset: -2px;
    padding: 2px;
    background: linear-gradient(45deg, #3b82f6, #8b5cf6, #06b6d4, #3b82f6);
    border-radius: inherit;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
}

/* 移除边框发光旋转动画 */

/* 移除悬浮粒子动画效果 */

/* 简化呼吸光效 - 移除动画 */
.breathing-glow {
    box-shadow:
        0 0 20px rgba(59, 130, 246, 0.3),
        inset 0 0 20px rgba(59, 130, 246, 0.1);
}

/* 移动端通用优化 */
@media (max-width: 768px) {
    /* 确保主容器正确使用视口高度 */
    .flex.h-screen {
        height: 100vh;
        min-height: 100vh;
        /* 兼容iOS Safari */
        min-height: -webkit-fill-available;
    }

    /* 主内容区域优化 */
    main.flex-1 {
        height: calc(100vh - 5rem); /* 减去头部高度 */
        overflow-y: auto;
        position: relative;
        padding-top: 0; /* 重置内边距 */
    }

    /* 详情页面特定优化 */
    #detail-content {
        /* 使用calc确保不会超出视口 */
        height: calc(100vh - 5rem);
        max-height: calc(100vh - 5rem);
        overflow-y: auto;
        -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */
        padding-top: 2.5rem; /* 大幅增加顶部padding，确保内容完全不被遮挡 */
        padding-left: 0.5rem;
        padding-right: 0.5rem;
        padding-bottom: 1rem;
    }

    /* 会议基本信息卡片优化 */
    #detail-content .glass-effect:first-child {
        flex-shrink: 0; /* 防止头部信息被压缩 */
        margin-bottom: 0.5rem;
        margin-top: 4rem; /* 大幅增加顶部间距，确保在所有手机上完全可见 */
        position: relative; /* 确保正常文档流 */
    }

    /* 确保详情页内容不会覆盖导航栏 */
    #detail-content > .glass-effect:first-child {
        margin-top: 4rem !important; /* 大幅增加顶部间距 */
        position: static !important;
        z-index: 1; /* 确保在导航栏下方 */
    }

    /* 确保顶部导航栏在最上层 */
    header {
        position: relative;
        z-index: 50; /* 确保导航栏在所有内容之上 */
    }

    /* 详情页内容容器 */
    #detail-content {
        position: relative;
        z-index: 1; /* 确保在导航栏下方 */
    }

    /* AI摘要区域优化 */
    #meeting-summary {
        max-height: 300px;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
    }

    /* 转录内容区域优化 */
    #transcript-content {
        max-height: 40vh;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
    }

    /* 音频播放器容器优化 */
    .bg-slate-800\/50.rounded-lg {
        margin-bottom: 0.5rem;
    }

    /* 移动端按钮优化 */
    #detail-content button {
        min-height: 44px; /* 确保触摸友好 */
        touch-action: manipulation; /* 优化触摸响应 */
    }

    /* 确保所有flex容器正确处理overflow */
    #detail-content .flex-1.flex.flex-col {
        min-height: 0;
        overflow: hidden;
    }

    /* 侧边栏在移动端的显示优化 */
    #sidebar {
        /* 确保侧边栏在移动端正确显示 */
        height: 100vh;
        min-height: 100vh;
        /* 兼容iOS Safari */
        min-height: -webkit-fill-available;
        /* 移动端侧边栏宽度 */
        width: 85%;
        max-width: 320px;
    }

    /* 搜索输入框 */
    #search-input {
        font-size: 1rem;
        padding: 0.75rem 1rem 0.75rem 2.5rem;
    }

    /* 筛选按钮 */
    .filter-btn {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
    }

    /* 空状态 */
    #empty-state, #no-results-state {
        height: 12rem;
        padding: 1.5rem;
    }

    #empty-state .w-16.h-16,
    #no-results-state .w-16.h-16 {
        width: 3rem;
        height: 3rem;
    }

    #empty-state p,
    #no-results-state p {
        font-size: 0.875rem;
    }

    /* 会议列表容器优化 */
    #meeting-list {
        /* 确保列表容器正确使用剩余空间 */
        flex: 1;
        min-height: 0;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
    }

    /* 会议项点击区域优化 */
    .meeting-item {
        /* 确保足够的点击区域 */
        min-height: 60px;
        touch-action: manipulation;
        /* 防止长按选择文本 */
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        /* 移动端padding */
        padding: 0.75rem;
    }

    /* 修复可能的滚动问题 */
    body {
        overflow-x: hidden;
        /* 防止水平滚动 */
        position: relative;
    }
}

/* 超小屏幕特殊优化 */
@media (max-width: 480px) {
    /* 进一步减少间距 */
    #detail-content {
        padding: 0.25rem;
    }

    /* 紧凑的卡片布局 */
    #detail-content .glass-effect {
        padding: 0.75rem;
        margin-bottom: 0.5rem;
    }

    /* 更小的字体和间距 */
    #detail-content h1 {
        font-size: 1.25rem;
    }

    #detail-content h2 {
        font-size: 1rem;
    }

    #detail-content h3 {
        font-size: 0.875rem;
    }
}

/* 移动端遮罩层优化 - 确保不阻挡侧边栏点击 */
#sidebar-overlay {
    /* 让遮罩层不阻挡侧边栏的点击事件 */
    pointer-events: none;
}

/* 移动端详情页顶部操作栏优化 */
@media (max-width: 768px) {
    /* 状态标签在移动端的样式 */
    #meeting-status {
        font-size: 0.625rem;
        padding: 0.25rem 0.5rem;
        margin-left: 0.25rem;
    }

    /* 会议信息在移动端的优化 */
    .flex.flex-wrap.items-center.gap-3 > span {
        font-size: 0.6875rem;
        white-space: nowrap;
    }

    /* 操作按钮组在中等屏幕的优化 */
    .flex.items-center.space-x-1 {
        gap: 0.375rem;
    }

    /* 确保所有按钮在一行显示 */
    .flex.items-center.justify-between.mb-2 {
        flex-wrap: nowrap;
        min-height: 2.5rem;
    }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
    /* 保持操作栏水平布局，不换行 */
    .flex.items-center.justify-between.mb-2 {
        flex-wrap: nowrap;
        overflow-x: auto;
        gap: 0.25rem;
    }

    /* 优化操作按钮在移动端的布局 */
    .flex.items-center.justify-between.mb-2 > div {
        flex-shrink: 0;
        min-width: fit-content;
    }

    /* 移动端操作按钮区域优化 */
    .flex.items-center.space-x-1 {
        gap: 0.25rem;
        flex-wrap: nowrap;
        overflow-x: auto;
        padding: 0 0.125rem;
    }

    /* 操作按钮统一样式 */
    .flex.items-center.space-x-1 button {
        flex-shrink: 0;
        min-width: 2rem;
        min-height: 2rem;
        padding: 0.25rem;
    }

    /* 状态标签在超小屏幕上的优化 */
    #meeting-status {
        font-size: 0.5rem;
        padding: 0.125rem 0.375rem;
        margin-left: 0.25rem;
        white-space: nowrap;
        flex-shrink: 0;
        min-width: fit-content;
    }

    /* 确保按钮图标大小一致 */
    .flex.items-center.space-x-1 i {
        font-size: 0.875rem;
        width: 0.875rem;
        text-align: center;
    }

    /* 首页内容在移动端的优化 */
    #home-content {
        padding: 0 1rem;
        max-height: calc(100vh - 8rem); /* 减去顶部导航栏高度 */
        overflow-y: auto;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    /* 移动端主容器高度优化 */
    .flex-1.flex.items-center.justify-center {
        padding: 0.5rem 1rem;
        min-height: calc(100vh - 4rem);
    }

    /* 功能卡片在移动端的紧凑显示 */
    .grid.grid-cols-3 {
        gap: 0.375rem;
        margin-top: 1rem;
    }

    /* 移动端三列卡片的文字优化 */
    .grid.grid-cols-3 .text-center {
        font-size: 0.625rem;
        line-height: 1.2;
    }

    /* 移动端卡片标题优化 */
    .grid.grid-cols-3 .font-medium {
        font-size: 0.625rem;
    }

    /* 移动端按钮图标调整 */
    #start-meeting-btn .fas.fa-play {
        margin-right: 0.5rem;
        font-size: 0.875rem;
    }

    /* 确保在更小屏幕上也能一屏显示 */
    @media (max-height: 700px) {
        #home-content > div:first-child {
            margin-bottom: 0.75rem; /* 进一步减少Logo区域间距 */
        }

        #home-content h1 {
            font-size: 1.5rem;
            margin-bottom: 0.25rem;
        }

        #home-content p {
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
        }

        /* 按钮区域进一步压缩 */
        .space-y-4 {
            margin-bottom: 0.75rem;
        }

        /* 功能卡片高度压缩 */
        .grid.grid-cols-3 > div > div:last-child {
            padding: 0.5rem 0.25rem;
        }

        /* 移动端三列卡片进一步压缩 */
        .grid.grid-cols-3 {
            gap: 0.25rem;
        }

        /* 超小屏幕卡片文字 */
        .grid.grid-cols-3 .font-medium {
            font-size: 0.5rem;
            margin-bottom: 0.125rem;
        }

        .grid.grid-cols-3 .text-center {
            font-size: 0.5rem;
            line-height: 1.1;
        }
    }
}

/* 完整记录标题区域简化 */
.flex.items-center.justify-between.p-3.bg-slate-800\/50 {
    justify-content: space-between;
}

/* 会议详情页标题优化 */
#meeting-title {
    /* 允许标题换行显示更多内容 */
    white-space: normal;
    word-wrap: break-word;
    line-height: 1.3;
    max-width: 100%;
}

/* 确保会议信息区域正常显示 */
.mb-3 {
    margin-bottom: 0.75rem;
    display: block !important;
    visibility: visible !important;
}

/* 会议信息项样式 */
.flex.flex-wrap.items-center.gap-3 {
    display: flex !important;
    flex-wrap: wrap;
    align-items: center;
    gap: 0.75rem;
    visibility: visible !important;
}

.flex.flex-wrap.items-center.gap-3 > span {
    display: flex !important;
    align-items: center;
    color: rgb(209 213 219); /* text-gray-300 */
    font-size: 0.75rem; /* text-xs */
    visibility: visible !important;
}

/* 确保会议信息的图标和文本都显示 */
.flex.flex-wrap.items-center.gap-3 > span > i {
    display: inline-block !important;
    margin-right: 0.25rem;
    color: rgb(96 165 250); /* text-primary-400 */
}

.flex.flex-wrap.items-center.gap-3 > span > span {
    display: inline !important;
}

@media (min-width: 640px) {
    .flex.flex-wrap.items-center.gap-3 > span {
        font-size: 0.875rem; /* sm:text-sm */
    }
}

/* 修复会议信息区域被遮挡的问题 */
@media (max-width: 768px) {
    /* 详情页主要内容区域 */
    #detail-content .flex-1.flex.flex-col.lg\\:flex-row {
        flex-direction: column;
        gap: 1rem;
        min-height: auto; /* 改为auto */
        height: auto;
        overflow: visible; /* 确保内容可见 */
    }

    /* 主要内容区域（会议信息） */
    #detail-content .flex-1.flex.flex-col.space-y-4 {
        min-height: auto; /* 改为auto */
        flex: none; /* 改为none，让内容自然伸展 */
        overflow: visible; /* 确保内容可见 */
        margin-bottom: 1rem;
    }

    /* 会议记录侧边栏在移动端的设置 */
    #detail-content .w-full.lg\\:w-96 {
        width: 100%;
        min-height: 300px;
        max-height: none; /* 移除最大高度限制 */
        overflow: visible; /* 确保内容可见 */
        flex-shrink: 0;
        margin-top: 1rem;
    }

    /* 会议信息卡片 */
    #detail-content .glass-effect {
        margin-bottom: 1rem;
        min-height: auto;
        overflow: visible;
        padding: 1rem;
    }
    
    /* 确保第一个卡片有足够的顶部间距 - 高优先级 */
    #detail-content .glass-effect:first-child {
        margin-top: 4rem !important; /* 使用!important确保不被覆盖 */
        flex-shrink: 0 !important;
        position: relative !important;
    }

    /* 会议摘要区域特别设置 */
    #meeting-summary {
        min-height: 150px; /* 确保有足够高度显示内容 */
        max-height: none; /* 移除高度限制 */
        overflow: visible; /* 确保内容可见 */
        padding: 0.5rem 0;
    }

    /* 确保转录内容也有足够空间 */
    #transcript-content {
        min-height: 200px;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
    }
}

/* 额外的移动端优化 */
@media (max-width: 768px) {
    /* 确保主容器可以滚动 */
    main.flex-1 {
        height: calc(100vh - 5rem);
        min-height: calc(100vh - 5rem);
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
        padding-top: 0;
    }

    /* 详情页容器 */
    #detail-content {
        height: calc(100vh - 5rem);
        min-height: calc(100vh - 5rem);
        max-height: calc(100vh - 5rem);
        overflow-y: auto;
        padding: 0.5rem;
        padding-top: 2.5rem; /* 大幅增加顶部padding，与上面的设置保持一致 */
        box-sizing: border-box;
    }

    /* 确保flexbox容器不会压缩内容 */
    #detail-content .flex-1.flex.flex-col.lg\\:flex-row {
        min-height: auto;
        height: auto;
    }

    /* 会议信息和完整记录都给足够空间 */
    #detail-content .flex-1.flex.flex-col.space-y-4,
    #detail-content .w-full.lg\\:w-96 {
        flex: none;
        width: 100%;
        margin-bottom: 1rem;
    }
}

/* 下载对话框样式 - 性能优化版本 */
.download-dialog {
    background: rgba(0, 0, 0, 0.5);
    animation: fadeInBackdrop 0.3s ease-out;
}

.download-dialog .bg-slate-800 {
    animation: slideInScale 0.3s ease-out;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

@keyframes fadeInBackdrop {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideInScale {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* 下载选项按钮样式 */
.download-option {
    position: relative;
    overflow: hidden;
}

.download-option::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.2), transparent);
    transition: left 0.5s;
}

.download-option:hover::before {
    left: 100%;
}

/* 下载按钮loading状态 */
.download-btn-loading {
    opacity: 0.7;
    pointer-events: none;
}

.download-btn-loading i {
    animation: spin 1s linear infinite;
}

/* 下载进度指示器 */
.download-progress {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(30, 41, 59, 0.95);
    border-radius: 8px;
    padding: 12px 16px;
    border: 1px solid rgba(59, 130, 246, 0.3);
    z-index: 1000;
    animation: slideInFromRight 0.3s ease-out;
}

@keyframes slideInFromRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 文件类型图标颜色 */
.download-option .fa-file-word {
    color: #2563eb;
}

.download-option .fa-file-text {
    color: #059669;
}

.download-option .fa-file-alt {
    color: #7c3aed;
}

.download-option .fa-file-audio {
    color: #dc2626;
}

/* 响应式下载对话框 */
@media (max-width: 768px) {
    .download-dialog .bg-slate-800 {
        margin: 1rem;
        max-width: calc(100vw - 2rem);
    }
    
    .download-option {
        padding: 1rem;
        font-size: 0.9rem;
    }
    
    .download-progress {
        top: 10px;
        right: 10px;
        left: 10px;
        padding: 10px 12px;
        font-size: 0.875rem;
    }
}

/* 移动端详情页顶部间距修复 - 最高优先级 */
@media (max-width: 768px) {
    /* 使用最高特异性确保第一个会议信息卡片不被遮挡 */
    body #detail-content .glass-effect:first-child,
    body main #detail-content .glass-effect:first-child,
    html body #detail-content .glass-effect:first-child {
        margin-top: 1rem !important;
        padding-top: 1rem !important;
        flex-shrink: 0 !important;
        position: relative !important;
        z-index: 1 !important;
    }
    
    /* 确保详情页容器有足够的顶部padding */
    body #detail-content,
    body main #detail-content,
    html body #detail-content {
        padding-top: 2.5rem !important;
        box-sizing: border-box !important;
    }
}
