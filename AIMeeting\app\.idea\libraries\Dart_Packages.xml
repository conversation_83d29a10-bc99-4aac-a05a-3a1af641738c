<component name="libraryTable">
  <library name="Dart Packages" type="DartPackagesLibraryType">
    <properties>
      <option name="packageNameToDirsMap">
        <entry key="async">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/async-2.11.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="characters">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/characters-1.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="clock">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/clock-1.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="collection">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/collection-1.18.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="convert">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/convert-3.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="ffi">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/ffi-2.1.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter">
          <value>
            <list>
              <option value="D:/Development/SDK/Flutter/flutter_3.19.6/packages/flutter/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_inappwebview">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview-6.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_inappwebview_android">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_android-1.0.13/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_inappwebview_internal_annotations">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_internal_annotations-1.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_inappwebview_ios">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.0.13/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_inappwebview_macos">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_macos-1.0.11/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_inappwebview_platform_interface">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_platform_interface-1.0.10/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_inappwebview_web">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_web-1.0.8/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_lints">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/flutter_lints-2.0.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_localizations">
          <value>
            <list>
              <option value="D:/Development/SDK/Flutter/flutter_3.19.6/packages/flutter_localizations/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_web_plugins">
          <value>
            <list>
              <option value="D:/Development/SDK/Flutter/flutter_3.19.6/packages/flutter_web_plugins/lib" />
            </list>
          </value>
        </entry>
        <entry key="http_parser">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/http_parser-4.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="intl">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/intl-0.18.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="js">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/js-0.6.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="lints">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/lints-2.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="material_color_utilities">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/material_color_utilities-0.8.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="meta">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/meta-1.11.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="mime">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/mime-2.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="path">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/path-1.9.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/path_provider-2.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_android">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/path_provider_android-2.2.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_foundation">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/path_provider_foundation-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_linux">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/path_provider_linux-2.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_platform_interface">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/path_provider_platform_interface-2.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_windows">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/path_provider_windows-2.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/permission_handler-11.3.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler_android">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_android-12.0.13/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler_apple">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler_html">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_html-0.1.3+5/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler_platform_interface">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_platform_interface-4.2.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler_windows">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_windows-0.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="platform">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/platform-3.1.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="plugin_platform_interface">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/plugin_platform_interface-2.1.8/lib" />
            </list>
          </value>
        </entry>
        <entry key="shelf">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/shelf-1.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="shelf_static">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/shelf_static-1.1.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="sky_engine">
          <value>
            <list>
              <option value="D:/Development/SDK/Flutter/flutter_3.19.6/bin/cache/pkg/sky_engine/lib" />
            </list>
          </value>
        </entry>
        <entry key="source_span">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/source_span-1.10.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="stack_trace">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/stack_trace-1.11.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="stream_channel">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/stream_channel-2.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="string_scanner">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/string_scanner-1.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="term_glyph">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/term_glyph-1.2.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="typed_data">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/typed_data-1.3.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="vector_math">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/vector_math-2.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="web">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/web-0.5.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="xdg_directories">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/xdg_directories-1.1.0/lib" />
            </list>
          </value>
        </entry>
      </option>
    </properties>
    <CLASSES>
      <root url="file://D:/Development/SDK/Flutter/flutter_3.19.6/bin/cache/pkg/sky_engine/lib" />
      <root url="file://D:/Development/SDK/Flutter/flutter_3.19.6/packages/flutter/lib" />
      <root url="file://D:/Development/SDK/Flutter/flutter_3.19.6/packages/flutter_localizations/lib" />
      <root url="file://D:/Development/SDK/Flutter/flutter_3.19.6/packages/flutter_web_plugins/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/async-2.11.0/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/characters-1.3.0/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/clock-1.1.1/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/collection-1.18.0/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/convert-3.1.1/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/ffi-2.1.3/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview-6.0.0/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_android-1.0.13/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_internal_annotations-1.2.0/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.0.13/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_macos-1.0.11/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_platform_interface-1.0.10/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_web-1.0.8/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/flutter_lints-2.0.3/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/http_parser-4.0.2/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/intl-0.18.1/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/js-0.6.7/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/lints-2.1.1/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/material_color_utilities-0.8.0/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/meta-1.11.0/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/mime-2.0.0/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/path-1.9.0/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/path_provider-2.1.2/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/path_provider_android-2.2.4/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/path_provider_foundation-2.4.1/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/path_provider_linux-2.2.1/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/path_provider_platform_interface-2.1.2/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/path_provider_windows-2.3.0/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/permission_handler-11.3.1/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_android-12.0.13/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_html-0.1.3+5/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_platform_interface-4.2.3/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/permission_handler_windows-0.2.1/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/platform-3.1.6/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/plugin_platform_interface-2.1.8/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/shelf-1.4.1/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/shelf_static-1.1.3/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/source_span-1.10.1/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/stack_trace-1.11.1/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/stream_channel-2.1.4/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/string_scanner-1.4.1/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/term_glyph-1.2.2/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/typed_data-1.3.2/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/vector_math-2.1.4/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/web-0.5.1/lib" />
      <root url="file://$PROJECT_DIR$/../../../../../../../../Temp/Flutter/.pub-cache/hosted/pub.flutter-io.cn/xdg_directories-1.1.0/lib" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>