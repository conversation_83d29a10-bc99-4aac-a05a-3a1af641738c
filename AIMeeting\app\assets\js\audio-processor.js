// 优化的AudioWorklet处理器 - 用于高效音频录制和处理
class AudioRecorderProcessor extends AudioWorkletProcessor {
    constructor() {
        super();

        // 音频配置
        this.config = {
            inputSampleRate: 48000,
            outputSampleRate: 16000,
            inputSampleBits: 16,
            outputSampleBits: 16,
            bufferSize: 4096,
            maxBufferSize: 16384 // 防止内存无限增长
        };

        // 计算重采样比率
        this.downsampleRatio = this.config.inputSampleRate / this.config.outputSampleRate;

        // 优化的缓冲区管理
        this.audioBuffer = {
            data: new Float32Array(this.config.maxBufferSize),
            writeIndex: 0,
            size: 0
        };

        // 重采样缓冲区（预分配，避免频繁创建）
        this.resampleBuffer = new Float32Array(Math.ceil(this.config.bufferSize / this.downsampleRatio));

        // PCM编码缓冲区（预分配）
        this.pcmBuffer = new ArrayBuffer(this.resampleBuffer.length * 2);
        this.pcmView = new DataView(this.pcmBuffer);

        // 性能监控
        this.stats = {
            processCount: 0,
            totalProcessTime: 0,
            lastResetTime: performance.now()
        };

        // 监听主线程消息
        this.port.onmessage = (event) => {
            this.handleMessage(event.data);
        };
    }

    // 处理消息
    handleMessage(data) {
        switch (data.command) {
            case 'clear':
                this.clearBuffer();
                break;
            case 'getStats':
                this.sendStats();
                break;
            case 'updateConfig':
                this.updateConfig(data.config);
                break;
        }
    }

    // 优化的音频处理主循环
    process(inputs, outputs, parameters) {
        const startTime = performance.now();

        const input = inputs[0];
        if (!input || input.length === 0) {
            return true;
        }

        const inputChannel = input[0];
        if (!inputChannel || inputChannel.length === 0) {
            return true;
        }

        // 检查缓冲区是否有足够空间
        if (this.audioBuffer.writeIndex + inputChannel.length > this.config.maxBufferSize) {
            // 缓冲区满了，强制处理现有数据
            this.processAndSendAudio();
        }

        // 将音频数据复制到预分配的缓冲区（避免创建新数组）
        this.audioBuffer.data.set(inputChannel, this.audioBuffer.writeIndex);
        this.audioBuffer.writeIndex += inputChannel.length;
        this.audioBuffer.size += inputChannel.length;

        // 当缓冲区达到处理阈值时，处理并发送数据
        if (this.audioBuffer.size >= this.config.bufferSize) {
            this.processAndSendAudio();
        }

        // 更新性能统计
        this.updateStats(startTime);

        return true;
    }
    
    // 优化的音频处理和发送
    processAndSendAudio() {
        try {
            if (this.audioBuffer.size === 0) {
                return;
            }

            // 高效重采样（使用预分配的缓冲区）
            const resampledLength = this.downsampleAudio();

            // 高效PCM编码（使用预分配的缓冲区）
            const pcmLength = this.encodePCMOptimized(resampledLength);

            // 创建实际大小的ArrayBuffer（避免发送多余数据）
            const outputBuffer = this.pcmBuffer.slice(0, pcmLength);

            // 发送到主线程
            this.port.postMessage({
                type: 'audioData',
                data: outputBuffer
            });

            // 清空缓冲区
            this.clearBuffer();

        } catch (error) {
            this.port.postMessage({
                type: 'error',
                error: error.message,
                stack: error.stack
            });
        }
    }
    
    // 优化的降采样算法（使用预分配缓冲区）
    downsampleAudio() {
        if (this.audioBuffer.size === 0) {
            return 0;
        }

        const inputData = this.audioBuffer.data;
        const inputLength = this.audioBuffer.size;
        const outputLength = Math.floor(inputLength / this.downsampleRatio);

        // 使用预分配的重采样缓冲区
        let outputIndex = 0;
        let inputIndex = 0;

        // 简单但高效的降采样（每N个样本取一个）
        while (outputIndex < outputLength && inputIndex < inputLength) {
            this.resampleBuffer[outputIndex] = inputData[Math.floor(inputIndex)];
            outputIndex++;
            inputIndex += this.downsampleRatio;
        }

        return outputIndex;
    }
    
    // 优化的PCM编码（使用预分配缓冲区）
    encodePCMOptimized(sampleCount) {
        if (sampleCount === 0) {
            return 0;
        }

        let offset = 0;
        for (let i = 0; i < sampleCount; i++, offset += 2) {
            // 将浮点数转换为16位整数，使用快速的位运算
            const sample = Math.max(-1, Math.min(1, this.resampleBuffer[i]));
            const intSample = sample < 0 ? (sample * 0x8000) | 0 : (sample * 0x7fff) | 0;
            this.pcmView.setInt16(offset, intSample, true);
        }

        return offset; // 返回实际使用的字节数
    }

    // 优化的缓冲区清理
    clearBuffer() {
        this.audioBuffer.writeIndex = 0;
        this.audioBuffer.size = 0;
        // 不需要重新分配数组，只需重置索引
    }

    // 性能统计更新
    updateStats(startTime) {
        this.stats.processCount++;
        this.stats.totalProcessTime += performance.now() - startTime;

        // 每1000次处理后重置统计（避免数值过大）
        if (this.stats.processCount >= 1000) {
            this.stats.processCount = 0;
            this.stats.totalProcessTime = 0;
            this.stats.lastResetTime = performance.now();
        }
    }

    // 发送性能统计
    sendStats() {
        const avgProcessTime = this.stats.processCount > 0 ?
            this.stats.totalProcessTime / this.stats.processCount : 0;

        this.port.postMessage({
            type: 'stats',
            data: {
                avgProcessTime: avgProcessTime,
                processCount: this.stats.processCount,
                bufferSize: this.audioBuffer.size,
                maxBufferSize: this.config.maxBufferSize
            }
        });
    }

    // 动态配置更新
    updateConfig(newConfig) {
        if (newConfig.bufferSize && newConfig.bufferSize !== this.config.bufferSize) {
            this.config.bufferSize = Math.max(1024, Math.min(16384, newConfig.bufferSize));
        }

        if (newConfig.maxBufferSize && newConfig.maxBufferSize !== this.config.maxBufferSize) {
            this.config.maxBufferSize = Math.max(this.config.bufferSize * 2, newConfig.maxBufferSize);
            // 重新分配缓冲区
            this.audioBuffer.data = new Float32Array(this.config.maxBufferSize);
            this.clearBuffer();
        }
    }
}

// 注册处理器
registerProcessor('audio-recorder-processor', AudioRecorderProcessor);
