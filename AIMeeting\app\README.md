# AI会议记录APP - Flutter客户端
这是一个使用Flutter框架开发的安卓手机移动端应用。
*   **功能角色**: 作为用户交互的GUI和音频数据的采集点。
*   **架构风格**: **无头Flutter主机 + 全功能H5单页应用 (Headless Flutter Host + Full-featured H5 SPA)**
*   **技术选型**: 整体框架-`Flutter 3.19.6、Dart 3.3.4`，Web服务器-`shelf`，WebView-`flutter_inappwebview`
*   **架构描述**:
	APP采用职责高度分离的混合架构。Flutter应用本身不包含任何可见的UI组件，其核心职责是作为一个稳定高效的后台服务主机。100%的用户界面和交互逻辑都由一个运行在全屏WebView中的H5单页应用（SPA）中来控制。
	1.  **无头Flutter主机 (The Headless Flutter Host)**:
		*   **角色**: 一个不可见的、提供原生能力的后台服务。
		*   **职责**:
			*   **WebView容器**: 启动后立即加载一个占满全屏的`flutter_inappwebview`组件。
			*   **本地Web服务器**: 通过`shelf`工具包，在Flutter本地`http://127.0.0.1`上运行一个Web服务器来托管H5应用资源，以规避`file://`协议的安全限制。
	2.  **全功能H5单页应用 (The Full-featured H5 SPA)**:
		*   **角色**: 应用的唯一用户界面和交互逻辑中心。
		*   **职责**:
			*   **UI渲染**: 负责渲染应用的所有视觉元素，包括侧边栏、主内容区、按钮、列表、表单、播放器等。
			*   **应用内导航**: 实现一个前端路由器，管理不同视图（主页与详情页）之间的切换，而无需重新加载整个Web页面。
			*   **状态管理**: 管理所有UI状态，如侧边栏的展开/收起、当前显示的会议信息等。
			*   **实时数据通信**: 这是**唯一直接与阿里云交互的任务**，在从后端获取到`MeetingJoinUrl`后，通过WebSocket (`wss://`) 直接与阿里AI云端服务进行通信，负责音频流的发送和实时转录结果的接收。
			*   **与后端服务交互**: 作为所有用户操作的起点，通过HTTP协议调用后端服务API接口来发起所有业务操作（开始/结束/查询会议），同时提供默认认证信息（在请求AUTH头中附加固定值）。