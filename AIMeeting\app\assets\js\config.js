// AI会议记录APP - 配置文件

const AppConfig = {
    // API配置
    api: {
        baseUrl: 'http://*************:8000/api/v1',
        authToken: 'your-auth-token-here', // 需要配置实际的认证令牌
        timeout: 30000,
        retryCount: 3,
        retryDelay: 1000
    },
    
    // WebSocket配置
    websocket: {
        maxRetryCount: 3,
        retryDelay: 2000,
        heartbeatInterval: 30000
    },
    
    // 音频配置
    audio: {
        sampleRate: 48000,
        outputSampleRate: 16000,
        channelCount: 1,
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true,
        chunkSize: 1024
    },
    
    // 会议配置
    meeting: {
        maxDuration: 4 * 60 * 60 * 1000, // 4小时
        transcriptUpdateInterval: 100,
        autoSaveInterval: 30000
    },
    
    // UI配置
    ui: {
        notificationDuration: 3000,
        loadingDelay: 500,
        animationDuration: 300
    },
    
    // 分页配置
    pagination: {
        defaultPageSize: 10,
        maxPageSize: 50
    }
};

// 环境配置
const Environment = {
    development: {
        api: {
            baseUrl: 'http://*************:8000/api/v1',
            authToken: '6L+H5b6X6aOe5b+r5o6g6L+H55qE77yM5byA5LqG5Liq77yM55qE5Lik5Liqdm1mbHM7bWZsbU06TE1PXiZUJjg3NzXliIbmrrXml7Y='
        },
        debug: true,
        logLevel: 'debug'
    },
    
    production: {
        api: {
            baseUrl: 'http://*************:8000/api/v1',
            authToken: '6L+H5b6X6aOe5b+r5o6g6L+H55qE77yM5byA5LqG5Liq77yM55qE5Lik5Liqdm1mbHM7bWZsbU06TE1PXiZUJjg3NzXliIbmrrXml7Y='
        },
        debug: false,
        logLevel: 'error'
    }
};

// 获取当前环境配置
function getCurrentEnvironment() {
    const hostname = window.location.hostname;
    
    if (hostname === 'localhost' || hostname === '127.0.0.1') {
        return 'development';
    } else {
        return 'production';
    }
}

// 合并配置
function getConfig() {
    const env = getCurrentEnvironment();
    const envConfig = Environment[env] || Environment.development;
    
    return {
        ...AppConfig,
        ...envConfig,
        api: {
            ...AppConfig.api,
            ...envConfig.api
        }
    };
}

// 导出配置
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { AppConfig, Environment, getConfig };
} else {
    window.AppConfig = AppConfig;
    window.Environment = Environment;
    window.getConfig = getConfig;
}
