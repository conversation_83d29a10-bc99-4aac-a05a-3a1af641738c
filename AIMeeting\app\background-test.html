<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深空背景效果测试</title>
    <script src="./assets/js/tailwindcss.js"></script>
    <link rel="stylesheet" href="assets/css/styles.css">
    <style>
        /* 测试页面样式 */
        .test-content {
            position: relative;
            z-index: 10;
            padding: 2rem;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .test-card {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 1rem;
            padding: 2rem;
            margin: 1rem 0;
        }
    </style>
</head>
<body class="dark bg-dark-900 text-white min-h-screen font-sans overflow-hidden">
    <!-- 高性能深空科技背景 -->
    <div class="fixed inset-0 z-0 overflow-hidden bg-deep-space"></div>
    
    <!-- 测试内容 -->
    <div class="test-content">
        <div class="test-card">
            <h1 class="text-3xl font-bold mb-4 text-center bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                深空科技背景效果测试
            </h1>
            <p class="text-gray-300 text-center mb-6">
                这是一个测试页面，用于验证新的高性能深空背景效果
            </p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="test-card">
                    <h3 class="text-lg font-semibold mb-2 text-blue-400">🎨 视觉特性</h3>
                    <ul class="text-sm text-gray-300 space-y-1">
                        <li>• 柔和的蓝青紫层叠渐变</li>
                        <li>• 微妙的径向光晕效果</li>
                        <li>• 移除格子图案，更加柔和</li>
                        <li>• 深空径向渐变基调</li>
                    </ul>
                </div>
                
                <div class="test-card">
                    <h3 class="text-lg font-semibold mb-2 text-purple-400">⚡ 性能优化</h3>
                    <ul class="text-sm text-gray-300 space-y-1">
                        <li>• 单一DOM节点</li>
                        <li>• CSS多重背景技术</li>
                        <li>• GPU硬件加速</li>
                        <li>• 响应式适配</li>
                    </ul>
                </div>
            </div>
            
            <div class="test-card mt-6">
                <h3 class="text-lg font-semibold mb-2 text-cyan-400">📱 设备适配</h3>
                <div class="text-sm text-gray-300">
                    <p><strong>桌面端：</strong>完整的多层柔和渐变效果</p>
                    <p><strong>平板端：</strong>5层蓝青紫渐变光晕</p>
                    <p><strong>手机端：</strong>3层简化渐变，无格子图案</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
