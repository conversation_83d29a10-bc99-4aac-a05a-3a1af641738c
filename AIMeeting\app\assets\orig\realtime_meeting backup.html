<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8" />
  <meta name="viewport"
    content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0" />
  <title>实时转录示例代码_本文件仅作为备份请勿修改</title>
  <style>
    div {
      display: flex;
      align-items: center;
      justify-content: center;
      padding-top: 100px;
    }

    #recorderStart,
    #recorderEnd {
      width: 100px;
      height: 40px;
      border: 1px solid red;
      background-color: #fff;
      margin-right: 20px;
      cursor: pointer;
    }
  </style>
</head>

<body>
  <div>
    <button id="recorderStart">开始录音</button>
    <button id="recorderEnd">关闭录音</button>

  </div>
</body>
<script>
  var ws = null; //WebSocket 实例
  var globalRecorder = null; //录音机/音频对象

  function connectWebSocket() {
    ws = new WebSocket("wss://tingwu-realtime-cn-beijing.aliyuncs.com/api/ws/v1?mc=3fVfi4-7D8FyKuathCPZEZBBfJIVVVh6wTK8hHpokUaZgxGGln8aeZu09-9iRow-CJM1g2U0lVzG23LLTSnD_WKgBVm-6CqOlaRLnSxs-m7gWaYk2IfgFxg2uMosOhdV");
    ws.binaryType = "arraybuffer"; //传输的是 ArrayBuffer 类型的数据
    ws.onopen = function () {
      if (ws.readyState == 1) {
        globalRecorder.start();
      }
      const params = {
        header: {
          name: "StartTranscription",
          namespace: "SpeechTranscriber",
        },
        payload: {
          format: "pcm", // 示例为pcm格式，可按需选择格式。注意如果非pcm格式，则样例中encodePCM方法不能使用，需自行实现数据格式转换。
        },
      };
      ws.send(JSON.stringify(params));
    };

    ws.onmessage = function (msg) {
      // console.info(msg);
      // msg.data 是接收到的数据（具体可参考「实时推流返回事件」），需要根据业务进行处理
      if (typeof msg.data === "string") {
        const dataJson = JSON.parse(msg.data);
        switch (dataJson.header.name) {
          case "SentenceBegin": {
            // 句子开始事件
            console.log("句子", dataJson.payload.index, "开始");
            break;
          }
          case "TranscriptionResultChanged": {
            // 句中识别结果变化事件
            console.log(
              "句子" + dataJson.payload.index + "中间结果:",
              dataJson.payload.result
            );
            break;
          }
          case "SentenceEnd": {
            // 句子结束事件
            console.log(
              "句子" + dataJson.payload.index + "结束:",
              dataJson.payload.result + dataJson.payload.stash_result.text
            );
            break;
          }
          case "ResultTranslated": {
            // 识别结果翻译事件
            console.log(
              "句子翻译结果",
              JSON.stringify(dataJson.payload.translate_result)
            );
            break;
          }
        }
      }
    };
    ws.onerror = function (err) {
      console.info(err);
    };
  }
</script>
<script type="text/javascript">
  //录音机 配置/数据处理
  var Recorder = function (stream) {
    var context = new (window.AudioContext || window.webkitAudioContext)();
    var audioInput = context.createMediaStreamSource(stream);
    var recorder = context.createScriptProcessor(4096, 1, 1);
    var audioData = {
      size: 0, //录音文件长度
      buffer: [], //录音缓存
      inputSampleRate: 48000, //输入采样率; 一般为本机浏览器默认采样率
      inputSampleBits: 16, //输入采样数位
      outputSampleRate: 16000, //输出采样率
      oututSampleBits: 16, //输出采样位
      clear: function () {
        this.buffer = [];
        this.size = 0;
      },
      input: function (data) {
        this.buffer.push(new Float32Array(data));
        this.size += data.length;
      },
      compress: function () {
        //对数据 进行 合并压缩
        var data = new Float32Array(this.size);
        var offset = 0;
        for (var i = 0; i < this.buffer.length; i++) {
          data.set(this.buffer[i], offset);
          offset += this.buffer[i].length;
        }

        var compression = parseInt(
          this.inputSampleRate / this.outputSampleRate
        );
        var length = data.length / compression;
        var result = new Float32Array(length);
        var index = 0,
          j = 0;
        while (index < length) {
          result[index] = data[j];
          j += compression;
          index++;
        }
        return result;
      },
      encodePCM: function () {
        var sampleRate = Math.min(
          this.inputSampleRate,
          this.outputSampleRate
        );
        var sampleBits = Math.min(this.inputSampleBits, this.oututSampleBits);
        var bytes = this.compress();
        var dataLength = bytes.length * (sampleBits / 8);
        var buffer = new ArrayBuffer(dataLength);
        var data = new DataView(buffer);
        var offset = 0;
        for (var i = 0; i < bytes.length; i++, offset += 2) {
          var s = Math.max(-1, Math.min(1, bytes[i]));
          data.setInt16(offset, s < 0 ? s * 0x8000 : s * 0x7fff, true);
        }
        return new Blob([data]);
      },
    };

    var sendAudioData = function () {
      //对数据 分片处理
      var reader = new FileReader();
      reader.onload = (e) => {
        var outbuffer = e.target.result;
        var arr = new Int8Array(outbuffer);
        if (arr.length > 0) {
          var tmparr = new Int8Array(1024);
          var j = 0;
          for (var i = 0; i < arr.byteLength; i++) {
            tmparr[j++] = arr[i];
            if ((i + 1) % 1024 == 0) {
              ws.send(tmparr);
              if (arr.byteLength - i - 1 >= 1024) {
                tmparr = new Int8Array(1024);
              } else {
                tmparr = new Int8Array(arr.byteLength - i - 1);
              }
              j = 0;
            }
            if (i + 1 == arr.byteLength && (i + 1) % 1024 != 0) {
              ws.send(tmparr);
            }
          }
        }
      };
      reader.readAsArrayBuffer(audioData.encodePCM());
      audioData.clear();
    };

    this.start = function () {
      audioInput.connect(recorder);
      recorder.connect(context.destination);
    };

    this.stop = function () {
      recorder.disconnect();
    };

    this.clear = function () {
      audioData.clear();
    };

    recorder.onaudioprocess = function (e) {
      var inputBuffer = e.inputBuffer.getChannelData(0);
      audioData.input(inputBuffer);
      sendAudioData();
    };
  };

  /*
   * 开始录音
   */
  document.getElementById("recorderStart").onclick = function () {
    /**
     * 注：以下示例是使用浏览器原生 navigator.getUserMedia方法，基础的实现验证听悟API服务可调通。您可以根据业务场景需要，选用合适的录音库或录音方法，实现更多设备兼容（如浏览器Web&H5、小程序、NativeApp等，不同场景的录音方法不同）
     */
    navigator.getUserMedia =
      navigator.getUserMedia || navigator.webkitGetUserMedia;
    if (!navigator.getUserMedia) {
      console.log("浏览器不支持音频输入");
    } else {
      navigator.getUserMedia(
        {
          audio: true,
        },
        (stream) => {
          globalRecorder = new Recorder(stream);
          console.log("开始录音");
          connectWebSocket();
        },
        (error) => {
          console.log(error);
          // TODO 一些异常错误处理
        }
      );
    }
  };

  /*
   * 关闭录音
   */
  document.getElementById("recorderEnd").onclick = function () {
    if (ws) {
      const params = {
        header: {
          name: "StopTranscription",
          namespace: "SpeechTranscriber",
        },
        payload: {},
      };
      ws.send(JSON.stringify(params));
      setTimeout(() => {
        ws.close();
      }, 10000);
      globalRecorder.stop();
    }
  };
</script>

</html>