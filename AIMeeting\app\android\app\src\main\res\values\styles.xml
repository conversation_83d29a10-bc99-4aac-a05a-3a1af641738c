<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Theme applied to the Android Window while the process is starting when the OS's Dark Mode setting is off -->
    <style name="LaunchTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <!-- Show a splash screen on the activity. Automatically removed when
             the Flutter engine draws its first frame -->
        <item name="android:windowBackground">@drawable/launch_background</item>
        <item name="android:windowFullscreen">true</item>
    </style>


    <!--    <style name="App.Starting" parent="Theme.SplashScreen">-->
    <!--        &lt;!&ndash; 启动页背景色 &ndash;&gt;-->
    <!--        &lt;!&ndash; 假设你的 launch_background.xml 里用的是白色背景 &ndash;&gt;-->
    <!--        <item name="windowSplashScreenBackground">@drawable/splash_image_background</item>-->
    <!--        &lt;!&ndash; <item name="windowSplashScreenBackground">#FF0000</item>&ndash;&gt;-->

    <!--        &lt;!&ndash; 启动页中心图标 &ndash;&gt;-->
    <!--        &lt;!&ndash; 这里直接引用你放在 mipmap 文件夹下的图片 &ndash;&gt;-->
    <!--        &lt;!&ndash; <item name="windowSplashScreenAnimatedIcon">@mipmap/launch_image</item>&ndash;&gt;-->

    <!--        &lt;!&ndash; 状态栏是否在启动页期间显示。true为隐藏，false为显示 &ndash;&gt;-->
    <!--        <item name="android:windowFullscreen">true</item>-->

    <!--        &lt;!&ndash; 启动页消失后，应用将采用的主题 &ndash;&gt;-->
    <!--        <item name="postSplashScreenTheme">@style/NormalTheme</item>-->
    <!--    </style>-->


    <!-- Theme applied to the Android Window as soon as the process has started.
         This theme determines the color of the Android Window while your
         Flutter UI initializes, as well as behind your Flutter UI while its
         running.

         This Theme is only used starting with V2 of Flutter's Android embedding. -->
    <style name="NormalTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style>
</resources>
